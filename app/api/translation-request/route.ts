import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { sendToTelegram } from '@/lib/telegram'
import { translationRequestSchema } from '@/lib/validation'
import { writeFile, mkdir } from 'fs/promises'
import { join } from 'path'
import { existsSync } from 'fs'

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    
    // Extract form fields
    const data = {
      fullName: formData.get('fullName') as string,
      companyName: formData.get('companyName') as string || undefined,
      email: formData.get('email') as string,
      sourceLanguage: formData.get('sourceLanguage') as string,
      targetLanguage: formData.get('targetLanguage') as string,
      deadline: formData.get('deadline') as string,
      documentUrl: formData.get('documentUrl') as string || undefined,
      additionalInfo: formData.get('additionalInfo') as string || undefined,
    }

    // Validate the form data
    const validatedData = translationRequestSchema.parse(data)

    // Handle file upload if present
    const file = formData.get('documentFile') as File | null
    let documentUrl = validatedData.documentUrl
    let documentFileName: string | undefined

    if (file) {
      // Create uploads directory if it doesn't exist
      const uploadsDir = join(process.cwd(), 'uploads')
      if (!existsSync(uploadsDir)) {
        await mkdir(uploadsDir, { recursive: true })
      }

      // Generate unique filename
      const timestamp = Date.now()
      const originalName = file.name
      const extension = originalName.split('.').pop()
      documentFileName = `${timestamp}-${originalName}`
      const filePath = join(uploadsDir, documentFileName)

      // Save file
      const bytes = await file.arrayBuffer()
      const buffer = Buffer.from(bytes)
      await writeFile(filePath, buffer)

      // Set document URL to the uploaded file path
      documentUrl = `/uploads/${documentFileName}`
    }

    // Save to database
    const translationRequest = await prisma.translationRequest.create({
      data: {
        fullName: validatedData.fullName,
        companyName: validatedData.companyName,
        email: validatedData.email,
        sourceLanguage: validatedData.sourceLanguage,
        targetLanguage: validatedData.targetLanguage,
        deadline: new Date(validatedData.deadline),
        documentUrl,
        documentFileName,
        additionalInfo: validatedData.additionalInfo,
      },
    })

    // Send to Telegram
    const telegramData = {
      fullName: validatedData.fullName,
      companyName: validatedData.companyName,
      email: validatedData.email,
      sourceLanguage: validatedData.sourceLanguage,
      targetLanguage: validatedData.targetLanguage,
      deadline: validatedData.deadline,
      documentUrl,
      documentFileName,
      additionalInfo: validatedData.additionalInfo,
    }

    const telegramSent = await sendToTelegram(telegramData)

    // Update telegram status in database
    if (telegramSent) {
      await prisma.translationRequest.update({
        where: { id: translationRequest.id },
        data: { telegramSent: true },
      })
    }

    return NextResponse.json({
      success: true,
      data: {
        id: translationRequest.id,
        telegramSent,
      },
      message: 'Translation request submitted successfully',
    })

  } catch (error) {
    console.error('Translation request error:', error)

    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation error',
          details: error.message,
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
      },
      { status: 500 }
    )
  }
}
