import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({ subsets: ['latin', 'cyrillic'] })

export const metadata: Metadata = {
  title: 'Globaling - Professional Translation Services',
  description: 'Professional translation services for documents, websites, and more. Expert translators for healthcare, legal, technology, finance, and education industries.',
  keywords: 'translation, localization, interpretation, document translation, website localization, professional translators, tarjima, переводы, 번역',
  authors: [{ name: 'Globaling Team' }],
  openGraph: {
    title: 'Globaling - Professional Translation Services',
    description: 'Professional translation services for documents, websites, and more.',
    type: 'website',
    locale: 'uz_UZ',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="uz" dir="ltr">
      <body className={inter.className}>
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
          {children}
        </div>
      </body>
    </html>
  )
}
