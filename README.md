# Globaling - Professional Translation Services Website

A modern, professional website for Globaling translation services company built with Next.js, TypeScript, Tailwind CSS, and MySQL.

## Features

### Main Features
- **Translation Request Form** with comprehensive validation
- **File Upload** support for documents (PDF, Word, Excel, PowerPoint, Text)
- **URL Input** option for document links
- **MySQL Database** integration for storing requests
- **Telegram Bot Integration** for instant notifications
- **Responsive Design** for mobile and desktop
- **Professional UI/UX** with modern design

### Form Fields
- Full Name (required)
- Company Name (optional)
- Email Address (required with validation)
- Source Language (required dropdown)
- Target Language (required dropdown)
- Deadline (required date picker, minimum tomorrow)
- Document Upload/URL (optional)
- Additional Information (optional textarea)

### Content Sections
- **Services**: Document Translation, Website Localization, Interpretation, Technical Translation, Legal Translation
- **Industries**: Healthcare, Legal, Technology, Finance, Education
- **Features**: 99% Accuracy, Fast Delivery, Expert Team, Certified Quality

## Tech Stack

- **Frontend**: Next.js 14, React 18, TypeScript
- **Styling**: Tailwind CSS
- **Forms**: React Hook Form with Zod validation
- **Database**: MySQL with Prisma ORM
- **Icons**: Lucide React
- **File Upload**: Multer
- **API Integration**: Telegram Bot API

## Setup Instructions

### Prerequisites
- Node.js 16+ 
- MySQL database
- npm or yarn

### Installation

1. **Clone and install dependencies**:
   ```bash
   cd globaling
   npm install
   ```

2. **Database Setup**:
   - Create a MySQL database named `globaling`
   - Update the `DATABASE_URL` in `.env.local` with your MySQL credentials
   - Generate Prisma client and run migrations:
   ```bash
   npx prisma generate
   npx prisma db push
   ```

3. **Environment Configuration**:
   - Copy `.env.local.example` to `.env.local`
   - Update the following variables:
     - `DATABASE_URL`: Your MySQL connection string
     - `TELEGRAM_BOT_TOKEN`: Your Telegram bot token (optional)
     - `TELEGRAM_CHAT_ID`: Your Telegram chat ID (optional)
     - `NEXTAUTH_SECRET`: A random secret for NextAuth

4. **Create uploads directory**:
   ```bash
   mkdir uploads
   ```

5. **Run the development server**:
   ```bash
   npm run dev
   ```

6. **Open your browser**:
   Navigate to [http://localhost:3000](http://localhost:3000)

### Telegram Bot Setup (Optional)

1. Create a new bot with [@BotFather](https://t.me/botfather) on Telegram
2. Get your bot token
3. Get your chat ID (you can use [@userinfobot](https://t.me/userinfobot))
4. Update `.env.local` with your bot token and chat ID
5. Restart the development server

## Database Schema

The application uses a single table `translation_requests` with the following fields:

- `id`: Unique identifier
- `full_name`: Client's full name
- `company_name`: Company name (optional)
- `email`: Client's email address
- `source_language`: Source language for translation
- `target_language`: Target language for translation
- `deadline`: Project deadline
- `document_url`: URL to uploaded document or external link
- `document_file_name`: Original filename of uploaded document
- `additional_info`: Additional project information
- `status`: Request status (PENDING, IN_PROGRESS, COMPLETED, CANCELLED)
- `telegram_sent`: Boolean flag for Telegram notification status
- `created_at`: Timestamp of request creation
- `updated_at`: Timestamp of last update

## API Endpoints

### POST /api/translation-request
Handles form submissions with the following features:
- Form validation using Zod
- File upload handling
- Database storage
- Telegram notification
- Error handling and response formatting

## File Structure

```
globaling/
├── app/
│   ├── api/
│   │   └── translation-request/
│   │       └── route.ts
│   ├── globals.css
│   ├── layout.tsx
│   └── page.tsx
├── components/
│   ├── Footer.tsx
│   ├── Header.tsx
│   ├── IndustriesSection.tsx
│   ├── ServicesSection.tsx
│   └── TranslationForm.tsx
├── lib/
│   ├── constants.ts
│   ├── prisma.ts
│   ├── telegram.ts
│   ├── utils.ts
│   └── validation.ts
├── prisma/
│   └── schema.prisma
├── types/
│   └── index.ts
├── uploads/ (created at runtime)
├── .env.local
├── .env.local.example
├── .gitignore
├── next.config.js
├── package.json
├── postcss.config.js
├── README.md
├── tailwind.config.js
└── tsconfig.json
```

## Customization

### Adding Languages
Edit `lib/constants.ts` to add more languages to the `LANGUAGES` array.

### Modifying Services/Industries
Update the `SERVICES` and `INDUSTRIES` arrays in `lib/constants.ts`.

### Styling
The website uses Tailwind CSS. Modify `tailwind.config.js` for theme customization.

### Form Validation
Update `lib/validation.ts` to modify form validation rules.

## Production Deployment

1. Set up a production MySQL database
2. Configure environment variables for production
3. Build the application: `npm run build`
4. Start the production server: `npm start`

## Support

For support or questions about this implementation, please refer to the documentation or create an issue in the project repository.
