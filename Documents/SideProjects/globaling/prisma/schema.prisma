// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model TranslationRequest {
  id                String   @id @default(cuid())
  fullName          String   @map("full_name")
  companyName       String?  @map("company_name")
  email             String
  sourceLanguage    String   @map("source_language")
  targetLanguage    String   @map("target_language")
  deadline          DateTime
  documentUrl       String?  @map("document_url")
  documentFileName  String?  @map("document_file_name")
  additionalInfo    String?  @map("additional_info") @db.Text
  status            RequestStatus @default(PENDING)
  telegramSent      Boolean  @default(false) @map("telegram_sent")
  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @updatedAt @map("updated_at")

  @@map("translation_requests")
}

enum RequestStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  CANCELLED
}
