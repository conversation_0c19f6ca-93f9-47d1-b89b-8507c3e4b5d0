import { Heart, Gave<PERSON>, <PERSON>pt<PERSON>, DollarSign, GraduationCap } from 'lucide-react'
import { INDUSTRIES } from '@/lib/constants'

const iconMap = {
  Heart,
  Gavel,
  Laptop,
  DollarSign,
  GraduationCap,
}

export default function IndustriesSection() {
  return (
    <section id="industries" className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Industries We Serve
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Our specialized translators have deep expertise across various industries, 
            ensuring your content is translated with the right terminology and context.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {INDUSTRIES.map((industry, index) => {
            const IconComponent = iconMap[industry.icon as keyof typeof iconMap]
            
            return (
              <div
                key={industry.id}
                className="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-300 border border-gray-100"
              >
                <div className="flex items-center mb-4">
                  <div className="bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg p-3 mr-4">
                    <IconComponent className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900">
                    {industry.title}
                  </h3>
                </div>
                <p className="text-gray-600 leading-relaxed">
                  {industry.description}
                </p>
              </div>
            )
          })}
        </div>

        <div className="mt-12 bg-gradient-to-r from-primary-600 to-primary-700 rounded-2xl p-8 text-center text-white">
          <h3 className="text-2xl font-bold mb-4">
            Don't See Your Industry?
          </h3>
          <p className="text-lg mb-6 opacity-90">
            We work with clients across many other sectors. Contact us to discuss your specific translation needs.
          </p>
          <button
            onClick={() => {
              const element = document.getElementById('request-form')
              if (element) {
                element.scrollIntoView({ behavior: 'smooth' })
              }
            }}
            className="bg-white text-primary-600 hover:bg-gray-100 px-8 py-3 rounded-lg font-medium text-lg transition-colors duration-200"
          >
            Get in Touch
          </button>
        </div>
      </div>
    </section>
  )
}
