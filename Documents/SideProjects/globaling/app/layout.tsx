import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Globaling - Professional Translation Services',
  description: 'Professional translation services for documents, websites, and more. Expert translators for healthcare, legal, technology, finance, and education industries.',
  keywords: 'translation, localization, interpretation, document translation, website localization, professional translators',
  authors: [{ name: 'Globaling Team' }],
  openGraph: {
    title: 'Globaling - Professional Translation Services',
    description: 'Professional translation services for documents, websites, and more.',
    type: 'website',
    locale: 'en_US',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
          {children}
        </div>
      </body>
    </html>
  )
}
