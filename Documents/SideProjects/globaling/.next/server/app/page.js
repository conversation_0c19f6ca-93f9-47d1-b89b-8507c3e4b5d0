/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fdoniyor%2FDocuments%2FSideProjects%2Fglobaling%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fdoniyor%2FDocuments%2FSideProjects%2Fglobaling&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fdoniyor%2FDocuments%2FSideProjects%2Fglobaling%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fdoniyor%2FDocuments%2FSideProjects%2Fglobaling&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"/Users/<USER>/Documents/SideProjects/globaling/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fdoniyor%2FDocuments%2FSideProjects%2Fglobaling%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fdoniyor%2FDocuments%2FSideProjects%2Fglobaling&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fdoniyor%2FDocuments%2FSideProjects%2Fglobaling%2Fapp%2Fglobals.css&modules=%2FUsers%2Fdoniyor%2FDocuments%2FSideProjects%2Fglobaling%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fdoniyor%2FDocuments%2FSideProjects%2Fglobaling%2Fapp%2Fglobals.css&modules=%2FUsers%2Fdoniyor%2FDocuments%2FSideProjects%2Fglobaling%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fdoniyor%2FDocuments%2FSideProjects%2Fglobaling%2Fcomponents%2FHeader.tsx&modules=%2FUsers%2Fdoniyor%2FDocuments%2FSideProjects%2Fglobaling%2Fcomponents%2FTranslationForm.tsx&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fdoniyor%2FDocuments%2FSideProjects%2Fglobaling%2Fcomponents%2FHeader.tsx&modules=%2FUsers%2Fdoniyor%2FDocuments%2FSideProjects%2Fglobaling%2Fcomponents%2FTranslationForm.tsx&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Header.tsx */ \"(ssr)/./components/Header.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/TranslationForm.tsx */ \"(ssr)/./components/TranslationForm.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZkb25peW9yJTJGRG9jdW1lbnRzJTJGU2lkZVByb2plY3RzJTJGZ2xvYmFsaW5nJTJGY29tcG9uZW50cyUyRkhlYWRlci50c3gmbW9kdWxlcz0lMkZVc2VycyUyRmRvbml5b3IlMkZEb2N1bWVudHMlMkZTaWRlUHJvamVjdHMlMkZnbG9iYWxpbmclMkZjb21wb25lbnRzJTJGVHJhbnNsYXRpb25Gb3JtLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsMEpBQTBHO0FBQzFHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZ2xvYmFsaW5nLXdlYnNpdGUvP2Y2ZDIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvZG9uaXlvci9Eb2N1bWVudHMvU2lkZVByb2plY3RzL2dsb2JhbGluZy9jb21wb25lbnRzL0hlYWRlci50c3hcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9kb25peW9yL0RvY3VtZW50cy9TaWRlUHJvamVjdHMvZ2xvYmFsaW5nL2NvbXBvbmVudHMvVHJhbnNsYXRpb25Gb3JtLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fdoniyor%2FDocuments%2FSideProjects%2Fglobaling%2Fcomponents%2FHeader.tsx&modules=%2FUsers%2Fdoniyor%2FDocuments%2FSideProjects%2Fglobaling%2Fcomponents%2FTranslationForm.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fdoniyor%2FDocuments%2FSideProjects%2Fglobaling%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fdoniyor%2FDocuments%2FSideProjects%2Fglobaling%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fdoniyor%2FDocuments%2FSideProjects%2Fglobaling%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fdoniyor%2FDocuments%2FSideProjects%2Fglobaling%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fdoniyor%2FDocuments%2FSideProjects%2Fglobaling%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fdoniyor%2FDocuments%2FSideProjects%2Fglobaling%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fdoniyor%2FDocuments%2FSideProjects%2Fglobaling%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fdoniyor%2FDocuments%2FSideProjects%2Fglobaling%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fdoniyor%2FDocuments%2FSideProjects%2Fglobaling%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fdoniyor%2FDocuments%2FSideProjects%2Fglobaling%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fdoniyor%2FDocuments%2FSideProjects%2Fglobaling%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fdoniyor%2FDocuments%2FSideProjects%2Fglobaling%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fdoniyor%2FDocuments%2FSideProjects%2Fglobaling%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fdoniyor%2FDocuments%2FSideProjects%2Fglobaling%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fdoniyor%2FDocuments%2FSideProjects%2Fglobaling%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fdoniyor%2FDocuments%2FSideProjects%2Fglobaling%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fdoniyor%2FDocuments%2FSideProjects%2Fglobaling%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fdoniyor%2FDocuments%2FSideProjects%2Fglobaling%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./components/Header.tsx":
/*!*******************************!*\
  !*** ./components/Header.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Globe_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Menu,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Menu,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Menu,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Header() {\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const scrollToSection = (sectionId)=>{\n        const element = document.getElementById(sectionId);\n        if (element) {\n            element.scrollIntoView({\n                behavior: \"smooth\"\n            });\n        }\n        setIsMenuOpen(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white/95 backdrop-blur-sm shadow-sm sticky top-0 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    className: \"h-8 w-8 text-primary-600\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Header.tsx\",\n                                    lineNumber: 23,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: \"Globaling\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Header.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Header.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"hidden md:flex items-center space-x-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>scrollToSection(\"services\"),\n                                    className: \"text-gray-700 hover:text-primary-600 font-medium transition-colors\",\n                                    children: \"Services\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Header.tsx\",\n                                    lineNumber: 29,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>scrollToSection(\"industries\"),\n                                    className: \"text-gray-700 hover:text-primary-600 font-medium transition-colors\",\n                                    children: \"Industries\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Header.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>scrollToSection(\"request-form\"),\n                                    className: \"bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-lg font-medium transition-colors\",\n                                    children: \"Get Quote\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Header.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Header.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                            className: \"md:hidden p-2 rounded-lg text-gray-700 hover:bg-gray-100\",\n                            children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Header.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 27\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Header.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 55\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Header.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Header.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, this),\n                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden py-4 border-t border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex flex-col space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>scrollToSection(\"services\"),\n                                className: \"text-left text-gray-700 hover:text-primary-600 font-medium transition-colors\",\n                                children: \"Services\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Header.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>scrollToSection(\"industries\"),\n                                className: \"text-left text-gray-700 hover:text-primary-600 font-medium transition-colors\",\n                                children: \"Industries\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Header.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>scrollToSection(\"request-form\"),\n                                className: \"text-left bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-lg font-medium transition-colors w-fit\",\n                                children: \"Get Quote\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Header.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Header.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Header.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Header.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Header.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./components/TranslationForm.tsx":
/*!****************************************!*\
  !*** ./components/TranslationForm.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TranslationForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hook-form */ \"(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(ssr)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Link_Loader2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Link,Loader2,Upload!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Link_Loader2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Link,Loader2,Upload!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Link_Loader2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Link,Loader2,Upload!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Link_Loader2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Link,Loader2,Upload!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/link.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Link_Loader2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Link,Loader2,Upload!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _lib_validation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/validation */ \"(ssr)/./lib/validation.ts\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/constants */ \"(ssr)/./lib/constants.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nfunction TranslationForm() {\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [submitStatus, setSubmitStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"idle\");\n    const [selectedFile, setSelectedFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [useFileUpload, setUseFileUpload] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const { register, handleSubmit, formState: { errors }, reset, watch } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_6__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(_lib_validation__WEBPACK_IMPORTED_MODULE_3__.translationRequestSchema)\n    });\n    const sourceLanguage = watch(\"sourceLanguage\");\n    const targetLanguage = watch(\"targetLanguage\");\n    const handleFileChange = (event)=>{\n        const file = event.target.files?.[0];\n        if (file) {\n            if (!(0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.isValidFileType)(file)) {\n                alert(\"Please select a valid file type (PDF, Word, Excel, PowerPoint, or Text)\");\n                return;\n            }\n            if (file.size > 10 * 1024 * 1024) {\n                alert(\"File size must be less than 10MB\");\n                return;\n            }\n            setSelectedFile(file);\n        }\n    };\n    const onSubmit = async (data)=>{\n        setIsSubmitting(true);\n        setSubmitStatus(\"idle\");\n        try {\n            const formData = new FormData();\n            // Add form fields\n            Object.entries(data).forEach(([key, value])=>{\n                if (value !== undefined && value !== \"\") {\n                    formData.append(key, value);\n                }\n            });\n            // Add file if selected\n            if (selectedFile && useFileUpload) {\n                formData.append(\"documentFile\", selectedFile);\n            }\n            const response = await fetch(\"/api/translation-request\", {\n                method: \"POST\",\n                body: formData\n            });\n            const result = await response.json();\n            if (result.success) {\n                setSubmitStatus(\"success\");\n                reset();\n                setSelectedFile(null);\n            } else {\n                setSubmitStatus(\"error\");\n                console.error(\"Submission error:\", result.error);\n            }\n        } catch (error) {\n            setSubmitStatus(\"error\");\n            console.error(\"Submission error:\", error);\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    if (submitStatus === \"success\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-2xl shadow-lg p-8 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Link_Loader2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-16 w-16 text-green-500 mx-auto mb-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-2xl font-bold text-gray-900 mb-2\",\n                    children: \"Request Submitted Successfully!\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 mb-6\",\n                    children: \"Thank you for your translation request. We'll review your requirements and get back to you within 24 hours.\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>setSubmitStatus(\"idle\"),\n                    className: \"bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-lg font-medium transition-colors\",\n                    children: \"Submit Another Request\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n            lineNumber: 90,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-2xl shadow-lg p-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                        children: \"Request a Translation Quote\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Fill out the form below and we'll provide you with a detailed quote for your translation project.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this),\n            submitStatus === \"error\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 bg-red-50 border border-red-200 rounded-lg p-4 flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Link_Loader2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"h-5 w-5 text-red-500 mr-2\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-red-700\",\n                        children: \"There was an error submitting your request. Please try again.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                lineNumber: 116,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit(onSubmit),\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"fullName\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Full Name *\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        ...register(\"fullName\"),\n                                        type: \"text\",\n                                        id: \"fullName\",\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors\", errors.fullName ? \"border-red-300\" : \"border-gray-300\"),\n                                        placeholder: \"Enter your full name\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.fullName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-1 text-sm text-red-600\",\n                                        children: errors.fullName.message\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"companyName\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Company Name\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        ...register(\"companyName\"),\n                                        type: \"text\",\n                                        id: \"companyName\",\n                                        className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors\",\n                                        placeholder: \"Enter your company name (optional)\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"email\",\n                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                children: \"Email Address *\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                ...register(\"email\"),\n                                type: \"email\",\n                                id: \"email\",\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors\", errors.email ? \"border-red-300\" : \"border-gray-300\"),\n                                placeholder: \"Enter your email address\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 11\n                            }, this),\n                            errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-red-600\",\n                                children: errors.email.message\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"sourceLanguage\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Source Language *\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        ...register(\"sourceLanguage\"),\n                                        id: \"sourceLanguage\",\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors\", errors.sourceLanguage ? \"border-red-300\" : \"border-gray-300\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"Select source language\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 15\n                                            }, this),\n                                            _lib_constants__WEBPACK_IMPORTED_MODULE_4__.LANGUAGES.map((lang)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: lang.name,\n                                                    children: lang.name\n                                                }, lang.code, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 17\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.sourceLanguage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-1 text-sm text-red-600\",\n                                        children: errors.sourceLanguage.message\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"targetLanguage\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Target Language *\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        ...register(\"targetLanguage\"),\n                                        id: \"targetLanguage\",\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors\", errors.targetLanguage ? \"border-red-300\" : \"border-gray-300\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"Select target language\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 15\n                                            }, this),\n                                            _lib_constants__WEBPACK_IMPORTED_MODULE_4__.LANGUAGES.filter((lang)=>lang.name !== sourceLanguage).map((lang)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: lang.name,\n                                                    children: lang.name\n                                                }, lang.code, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 17\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.targetLanguage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-1 text-sm text-red-600\",\n                                        children: errors.targetLanguage.message\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"deadline\",\n                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                children: \"Deadline *\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                ...register(\"deadline\"),\n                                type: \"date\",\n                                id: \"deadline\",\n                                min: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.getTomorrowDate)(),\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors\", errors.deadline ? \"border-red-300\" : \"border-gray-300\")\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 11\n                            }, this),\n                            errors.deadline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-red-600\",\n                                children: errors.deadline.message\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                children: \"Document\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setUseFileUpload(true),\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"px-4 py-2 rounded-lg font-medium transition-colors\", useFileUpload ? \"bg-primary-600 text-white\" : \"bg-gray-100 text-gray-700 hover:bg-gray-200\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Link_Loader2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-4 w-4 inline mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Upload File\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setUseFileUpload(false),\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"px-4 py-2 rounded-lg font-medium transition-colors\", !useFileUpload ? \"bg-primary-600 text-white\" : \"bg-gray-100 text-gray-700 hover:bg-gray-200\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Link_Loader2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-4 w-4 inline mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Provide URL\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 11\n                            }, this),\n                            useFileUpload ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"file\",\n                                        id: \"documentFile\",\n                                        onChange: handleFileChange,\n                                        accept: \".pdf,.doc,.docx,.txt,.xls,.xlsx,.ppt,.pptx\",\n                                        className: \"hidden\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"documentFile\",\n                                        className: \"w-full border-2 border-dashed border-gray-300 rounded-lg p-6 text-center cursor-pointer hover:border-primary-400 transition-colors block\",\n                                        children: selectedFile ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-900\",\n                                                    children: selectedFile.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatFileSize)(selectedFile.size)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Link_Loader2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-8 w-8 text-gray-400 mx-auto mb-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Click to upload or drag and drop\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500 mt-1\",\n                                                    children: \"PDF, Word, Excel, PowerPoint, or Text files (max 10MB)\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                ...register(\"documentUrl\"),\n                                type: \"url\",\n                                placeholder: \"https://example.com/document.pdf\",\n                                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                        lineNumber: 252,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"additionalInfo\",\n                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                children: \"Additional Information\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                ...register(\"additionalInfo\"),\n                                id: \"additionalInfo\",\n                                rows: 4,\n                                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors\",\n                                placeholder: \"Please provide any additional details about your translation project...\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                        lineNumber: 329,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"submit\",\n                        disabled: isSubmitting,\n                        className: \"w-full bg-primary-600 hover:bg-primary-700 disabled:bg-primary-400 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center\",\n                        children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Link_Loader2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-5 w-5 animate-spin mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                                    lineNumber: 350,\n                                    columnNumber: 15\n                                }, this),\n                                \"Submitting Request...\"\n                            ]\n                        }, void 0, true) : \"Submit Translation Request\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                        lineNumber: 343,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx\",\n        lineNumber: 107,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL1RyYW5zbGF0aW9uRm9ybS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUVnQztBQUNTO0FBQ1k7QUFDeUI7QUFDVztBQUM5QztBQUNtQztBQUM5QztBQUVqQixTQUFTYztJQUN0QixNQUFNLENBQUNDLGNBQWNDLGdCQUFnQixHQUFHaEIsK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDaUIsY0FBY0MsZ0JBQWdCLEdBQUdsQiwrQ0FBUUEsQ0FBK0I7SUFDL0UsTUFBTSxDQUFDbUIsY0FBY0MsZ0JBQWdCLEdBQUdwQiwrQ0FBUUEsQ0FBYztJQUM5RCxNQUFNLENBQUNxQixlQUFlQyxpQkFBaUIsR0FBR3RCLCtDQUFRQSxDQUFDO0lBRW5ELE1BQU0sRUFDSnVCLFFBQVEsRUFDUkMsWUFBWSxFQUNaQyxXQUFXLEVBQUVDLE1BQU0sRUFBRSxFQUNyQkMsS0FBSyxFQUNMQyxLQUFLLEVBQ04sR0FBRzNCLHdEQUFPQSxDQUEwQjtRQUNuQzRCLFVBQVUzQixvRUFBV0EsQ0FBQ00scUVBQXdCQTtJQUNoRDtJQUVBLE1BQU1zQixpQkFBaUJGLE1BQU07SUFDN0IsTUFBTUcsaUJBQWlCSCxNQUFNO0lBRTdCLE1BQU1JLG1CQUFtQixDQUFDQztRQUN4QixNQUFNQyxPQUFPRCxNQUFNRSxNQUFNLENBQUNDLEtBQUssRUFBRSxDQUFDLEVBQUU7UUFDcEMsSUFBSUYsTUFBTTtZQUNSLElBQUksQ0FBQ3RCLDJEQUFlQSxDQUFDc0IsT0FBTztnQkFDMUJHLE1BQU07Z0JBQ047WUFDRjtZQUNBLElBQUlILEtBQUtJLElBQUksR0FBRyxLQUFLLE9BQU8sTUFBTTtnQkFDaENELE1BQU07Z0JBQ047WUFDRjtZQUNBakIsZ0JBQWdCYztRQUNsQjtJQUNGO0lBRUEsTUFBTUssV0FBVyxPQUFPQztRQUN0QnhCLGdCQUFnQjtRQUNoQkUsZ0JBQWdCO1FBRWhCLElBQUk7WUFDRixNQUFNdUIsV0FBVyxJQUFJQztZQUVyQixrQkFBa0I7WUFDbEJDLE9BQU9DLE9BQU8sQ0FBQ0osTUFBTUssT0FBTyxDQUFDLENBQUMsQ0FBQ0MsS0FBS0MsTUFBTTtnQkFDeEMsSUFBSUEsVUFBVUMsYUFBYUQsVUFBVSxJQUFJO29CQUN2Q04sU0FBU1EsTUFBTSxDQUFDSCxLQUFLQztnQkFDdkI7WUFDRjtZQUVBLHVCQUF1QjtZQUN2QixJQUFJNUIsZ0JBQWdCRSxlQUFlO2dCQUNqQ29CLFNBQVNRLE1BQU0sQ0FBQyxnQkFBZ0I5QjtZQUNsQztZQUVBLE1BQU0rQixXQUFXLE1BQU1DLE1BQU0sNEJBQTRCO2dCQUN2REMsUUFBUTtnQkFDUkMsTUFBTVo7WUFDUjtZQUVBLE1BQU1hLFNBQVMsTUFBTUosU0FBU0ssSUFBSTtZQUVsQyxJQUFJRCxPQUFPRSxPQUFPLEVBQUU7Z0JBQ2xCdEMsZ0JBQWdCO2dCQUNoQlM7Z0JBQ0FQLGdCQUFnQjtZQUNsQixPQUFPO2dCQUNMRixnQkFBZ0I7Z0JBQ2hCdUMsUUFBUUMsS0FBSyxDQUFDLHFCQUFxQkosT0FBT0ksS0FBSztZQUNqRDtRQUNGLEVBQUUsT0FBT0EsT0FBTztZQUNkeEMsZ0JBQWdCO1lBQ2hCdUMsUUFBUUMsS0FBSyxDQUFDLHFCQUFxQkE7UUFDckMsU0FBVTtZQUNSMUMsZ0JBQWdCO1FBQ2xCO0lBQ0Y7SUFFQSxJQUFJQyxpQkFBaUIsV0FBVztRQUM5QixxQkFDRSw4REFBQzBDO1lBQUlDLFdBQVU7OzhCQUNiLDhEQUFDdkQsdUhBQVdBO29CQUFDdUQsV0FBVTs7Ozs7OzhCQUN2Qiw4REFBQ0M7b0JBQUdELFdBQVU7OEJBQXdDOzs7Ozs7OEJBQ3RELDhEQUFDRTtvQkFBRUYsV0FBVTs4QkFBcUI7Ozs7Ozs4QkFHbEMsOERBQUNHO29CQUNDQyxTQUFTLElBQU05QyxnQkFBZ0I7b0JBQy9CMEMsV0FBVTs4QkFDWDs7Ozs7Ozs7Ozs7O0lBS1A7SUFFQSxxQkFDRSw4REFBQ0Q7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0M7d0JBQUdELFdBQVU7a0NBQXdDOzs7Ozs7a0NBQ3RELDhEQUFDRTt3QkFBRUYsV0FBVTtrQ0FBZ0I7Ozs7Ozs7Ozs7OztZQUs5QjNDLGlCQUFpQix5QkFDaEIsOERBQUMwQztnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUN0RCx1SEFBV0E7d0JBQUNzRCxXQUFVOzs7Ozs7a0NBQ3ZCLDhEQUFDSzt3QkFBS0wsV0FBVTtrQ0FBZTs7Ozs7Ozs7Ozs7OzBCQU1uQyw4REFBQ007Z0JBQUszQixVQUFVZixhQUFhZTtnQkFBV3FCLFdBQVU7O2tDQUVoRCw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDs7a0RBQ0MsOERBQUNRO3dDQUFNQyxTQUFRO3dDQUFXUixXQUFVO2tEQUErQzs7Ozs7O2tEQUduRiw4REFBQ1M7d0NBQ0UsR0FBRzlDLFNBQVMsV0FBVzt3Q0FDeEIrQyxNQUFLO3dDQUNMQyxJQUFHO3dDQUNIWCxXQUFXL0MsOENBQUVBLENBQ1gscUhBQ0FhLE9BQU84QyxRQUFRLEdBQUcsbUJBQW1CO3dDQUV2Q0MsYUFBWTs7Ozs7O29DQUViL0MsT0FBTzhDLFFBQVEsa0JBQ2QsOERBQUNWO3dDQUFFRixXQUFVO2tEQUE2QmxDLE9BQU84QyxRQUFRLENBQUNFLE9BQU87Ozs7Ozs7Ozs7OzswQ0FJckUsOERBQUNmOztrREFDQyw4REFBQ1E7d0NBQU1DLFNBQVE7d0NBQWNSLFdBQVU7a0RBQStDOzs7Ozs7a0RBR3RGLDhEQUFDUzt3Q0FDRSxHQUFHOUMsU0FBUyxjQUFjO3dDQUMzQitDLE1BQUs7d0NBQ0xDLElBQUc7d0NBQ0hYLFdBQVU7d0NBQ1ZhLGFBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FNbEIsOERBQUNkOzswQ0FDQyw4REFBQ1E7Z0NBQU1DLFNBQVE7Z0NBQVFSLFdBQVU7MENBQStDOzs7Ozs7MENBR2hGLDhEQUFDUztnQ0FDRSxHQUFHOUMsU0FBUyxRQUFRO2dDQUNyQitDLE1BQUs7Z0NBQ0xDLElBQUc7Z0NBQ0hYLFdBQVcvQyw4Q0FBRUEsQ0FDWCxxSEFDQWEsT0FBT2lELEtBQUssR0FBRyxtQkFBbUI7Z0NBRXBDRixhQUFZOzs7Ozs7NEJBRWIvQyxPQUFPaUQsS0FBSyxrQkFDWCw4REFBQ2I7Z0NBQUVGLFdBQVU7MENBQTZCbEMsT0FBT2lELEtBQUssQ0FBQ0QsT0FBTzs7Ozs7Ozs7Ozs7O2tDQUtsRSw4REFBQ2Y7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDs7a0RBQ0MsOERBQUNRO3dDQUFNQyxTQUFRO3dDQUFpQlIsV0FBVTtrREFBK0M7Ozs7OztrREFHekYsOERBQUNnQjt3Q0FDRSxHQUFHckQsU0FBUyxpQkFBaUI7d0NBQzlCZ0QsSUFBRzt3Q0FDSFgsV0FBVy9DLDhDQUFFQSxDQUNYLHFIQUNBYSxPQUFPSSxjQUFjLEdBQUcsbUJBQW1COzswREFHN0MsOERBQUMrQztnREFBTzlCLE9BQU07MERBQUc7Ozs7Ozs0Q0FDaEJ0QyxxREFBU0EsQ0FBQ3FFLEdBQUcsQ0FBQyxDQUFDQyxxQkFDZCw4REFBQ0Y7b0RBQXVCOUIsT0FBT2dDLEtBQUtDLElBQUk7OERBQ3JDRCxLQUFLQyxJQUFJO21EQURDRCxLQUFLRSxJQUFJOzs7Ozs7Ozs7OztvQ0FLekJ2RCxPQUFPSSxjQUFjLGtCQUNwQiw4REFBQ2dDO3dDQUFFRixXQUFVO2tEQUE2QmxDLE9BQU9JLGNBQWMsQ0FBQzRDLE9BQU87Ozs7Ozs7Ozs7OzswQ0FJM0UsOERBQUNmOztrREFDQyw4REFBQ1E7d0NBQU1DLFNBQVE7d0NBQWlCUixXQUFVO2tEQUErQzs7Ozs7O2tEQUd6Riw4REFBQ2dCO3dDQUNFLEdBQUdyRCxTQUFTLGlCQUFpQjt3Q0FDOUJnRCxJQUFHO3dDQUNIWCxXQUFXL0MsOENBQUVBLENBQ1gscUhBQ0FhLE9BQU9LLGNBQWMsR0FBRyxtQkFBbUI7OzBEQUc3Qyw4REFBQzhDO2dEQUFPOUIsT0FBTTswREFBRzs7Ozs7OzRDQUNoQnRDLHFEQUFTQSxDQUFDeUUsTUFBTSxDQUFDSCxDQUFBQSxPQUFRQSxLQUFLQyxJQUFJLEtBQUtsRCxnQkFBZ0JnRCxHQUFHLENBQUMsQ0FBQ0MscUJBQzNELDhEQUFDRjtvREFBdUI5QixPQUFPZ0MsS0FBS0MsSUFBSTs4REFDckNELEtBQUtDLElBQUk7bURBRENELEtBQUtFLElBQUk7Ozs7Ozs7Ozs7O29DQUt6QnZELE9BQU9LLGNBQWMsa0JBQ3BCLDhEQUFDK0I7d0NBQUVGLFdBQVU7a0RBQTZCbEMsT0FBT0ssY0FBYyxDQUFDMkMsT0FBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU03RSw4REFBQ2Y7OzBDQUNDLDhEQUFDUTtnQ0FBTUMsU0FBUTtnQ0FBV1IsV0FBVTswQ0FBK0M7Ozs7OzswQ0FHbkYsOERBQUNTO2dDQUNFLEdBQUc5QyxTQUFTLFdBQVc7Z0NBQ3hCK0MsTUFBSztnQ0FDTEMsSUFBRztnQ0FDSFksS0FBS3pFLDJEQUFlQTtnQ0FDcEJrRCxXQUFXL0MsOENBQUVBLENBQ1gscUhBQ0FhLE9BQU8wRCxRQUFRLEdBQUcsbUJBQW1COzs7Ozs7NEJBR3hDMUQsT0FBTzBELFFBQVEsa0JBQ2QsOERBQUN0QjtnQ0FBRUYsV0FBVTswQ0FBNkJsQyxPQUFPMEQsUUFBUSxDQUFDVixPQUFPOzs7Ozs7Ozs7Ozs7a0NBS3JFLDhEQUFDZjs7MENBQ0MsOERBQUNRO2dDQUFNUCxXQUFVOzBDQUErQzs7Ozs7OzBDQUdoRSw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0c7NENBQ0NPLE1BQUs7NENBQ0xOLFNBQVMsSUFBTTFDLGlCQUFpQjs0Q0FDaENzQyxXQUFXL0MsOENBQUVBLENBQ1gsc0RBQ0FRLGdCQUNJLDhCQUNBOzs4REFHTiw4REFBQ2xCLHVIQUFNQTtvREFBQ3lELFdBQVU7Ozs7OztnREFBd0I7Ozs7Ozs7c0RBRzVDLDhEQUFDRzs0Q0FDQ08sTUFBSzs0Q0FDTE4sU0FBUyxJQUFNMUMsaUJBQWlCOzRDQUNoQ3NDLFdBQVcvQyw4Q0FBRUEsQ0FDWCxzREFDQSxDQUFDUSxnQkFDRyw4QkFDQTs7OERBR04sOERBQUNqQix3SEFBSUE7b0RBQUN3RCxXQUFVOzs7Ozs7Z0RBQXdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7NEJBTTdDdkMsOEJBQ0MsOERBQUNzQzs7a0RBQ0MsOERBQUNVO3dDQUNDQyxNQUFLO3dDQUNMQyxJQUFHO3dDQUNIYyxVQUFVckQ7d0NBQ1ZzRCxRQUFPO3dDQUNQMUIsV0FBVTs7Ozs7O2tEQUVaLDhEQUFDTzt3Q0FDQ0MsU0FBUTt3Q0FDUlIsV0FBVTtrREFFVHpDLDZCQUNDLDhEQUFDd0M7OzhEQUNDLDhEQUFDRztvREFBRUYsV0FBVTs4REFBcUN6QyxhQUFhNkQsSUFBSTs7Ozs7OzhEQUNuRSw4REFBQ2xCO29EQUFFRixXQUFVOzhEQUF5QmpELDBEQUFjQSxDQUFDUSxhQUFhbUIsSUFBSTs7Ozs7Ozs7Ozs7aUVBR3hFLDhEQUFDcUI7OzhEQUNDLDhEQUFDeEQsdUhBQU1BO29EQUFDeUQsV0FBVTs7Ozs7OzhEQUNsQiw4REFBQ0U7b0RBQUVGLFdBQVU7OERBQXdCOzs7Ozs7OERBR3JDLDhEQUFDRTtvREFBRUYsV0FBVTs4REFBNkI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7cURBUWxELDhEQUFDUztnQ0FDRSxHQUFHOUMsU0FBUyxjQUFjO2dDQUMzQitDLE1BQUs7Z0NBQ0xHLGFBQVk7Z0NBQ1piLFdBQVU7Ozs7Ozs7Ozs7OztrQ0FNaEIsOERBQUNEOzswQ0FDQyw4REFBQ1E7Z0NBQU1DLFNBQVE7Z0NBQWlCUixXQUFVOzBDQUErQzs7Ozs7OzBDQUd6Riw4REFBQzJCO2dDQUNFLEdBQUdoRSxTQUFTLGlCQUFpQjtnQ0FDOUJnRCxJQUFHO2dDQUNIaUIsTUFBTTtnQ0FDTjVCLFdBQVU7Z0NBQ1ZhLGFBQVk7Ozs7Ozs7Ozs7OztrQ0FLaEIsOERBQUNWO3dCQUNDTyxNQUFLO3dCQUNMbUIsVUFBVTFFO3dCQUNWNkMsV0FBVTtrQ0FFVDdDLDZCQUNDOzs4Q0FDRSw4REFBQ1Isd0hBQU9BO29DQUFDcUQsV0FBVTs7Ozs7O2dDQUE4Qjs7MkNBSW5EOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFNWiIsInNvdXJjZXMiOlsid2VicGFjazovL2dsb2JhbGluZy13ZWJzaXRlLy4vY29tcG9uZW50cy9UcmFuc2xhdGlvbkZvcm0udHN4PzdjYTIiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyB1c2VGb3JtIH0gZnJvbSAncmVhY3QtaG9vay1mb3JtJ1xuaW1wb3J0IHsgem9kUmVzb2x2ZXIgfSBmcm9tICdAaG9va2Zvcm0vcmVzb2x2ZXJzL3pvZCdcbmltcG9ydCB7IFVwbG9hZCwgTGluaywgQ2hlY2tDaXJjbGUsIEFsZXJ0Q2lyY2xlLCBMb2FkZXIyIH0gZnJvbSAnbHVjaWRlLXJlYWN0J1xuaW1wb3J0IHsgdHJhbnNsYXRpb25SZXF1ZXN0U2NoZW1hLCB0eXBlIFRyYW5zbGF0aW9uUmVxdWVzdElucHV0IH0gZnJvbSAnQC9saWIvdmFsaWRhdGlvbidcbmltcG9ydCB7IExBTkdVQUdFUyB9IGZyb20gJ0AvbGliL2NvbnN0YW50cydcbmltcG9ydCB7IGdldFRvbW9ycm93RGF0ZSwgZm9ybWF0RmlsZVNpemUsIGlzVmFsaWRGaWxlVHlwZSB9IGZyb20gJ0AvbGliL3V0aWxzJ1xuaW1wb3J0IHsgY24gfSBmcm9tICdAL2xpYi91dGlscydcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gVHJhbnNsYXRpb25Gb3JtKCkge1xuICBjb25zdCBbaXNTdWJtaXR0aW5nLCBzZXRJc1N1Ym1pdHRpbmddID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFtzdWJtaXRTdGF0dXMsIHNldFN1Ym1pdFN0YXR1c10gPSB1c2VTdGF0ZTwnaWRsZScgfCAnc3VjY2VzcycgfCAnZXJyb3InPignaWRsZScpXG4gIGNvbnN0IFtzZWxlY3RlZEZpbGUsIHNldFNlbGVjdGVkRmlsZV0gPSB1c2VTdGF0ZTxGaWxlIHwgbnVsbD4obnVsbClcbiAgY29uc3QgW3VzZUZpbGVVcGxvYWQsIHNldFVzZUZpbGVVcGxvYWRdID0gdXNlU3RhdGUodHJ1ZSlcblxuICBjb25zdCB7XG4gICAgcmVnaXN0ZXIsXG4gICAgaGFuZGxlU3VibWl0LFxuICAgIGZvcm1TdGF0ZTogeyBlcnJvcnMgfSxcbiAgICByZXNldCxcbiAgICB3YXRjaCxcbiAgfSA9IHVzZUZvcm08VHJhbnNsYXRpb25SZXF1ZXN0SW5wdXQ+KHtcbiAgICByZXNvbHZlcjogem9kUmVzb2x2ZXIodHJhbnNsYXRpb25SZXF1ZXN0U2NoZW1hKSxcbiAgfSlcblxuICBjb25zdCBzb3VyY2VMYW5ndWFnZSA9IHdhdGNoKCdzb3VyY2VMYW5ndWFnZScpXG4gIGNvbnN0IHRhcmdldExhbmd1YWdlID0gd2F0Y2goJ3RhcmdldExhbmd1YWdlJylcblxuICBjb25zdCBoYW5kbGVGaWxlQ2hhbmdlID0gKGV2ZW50OiBSZWFjdC5DaGFuZ2VFdmVudDxIVE1MSW5wdXRFbGVtZW50PikgPT4ge1xuICAgIGNvbnN0IGZpbGUgPSBldmVudC50YXJnZXQuZmlsZXM/LlswXVxuICAgIGlmIChmaWxlKSB7XG4gICAgICBpZiAoIWlzVmFsaWRGaWxlVHlwZShmaWxlKSkge1xuICAgICAgICBhbGVydCgnUGxlYXNlIHNlbGVjdCBhIHZhbGlkIGZpbGUgdHlwZSAoUERGLCBXb3JkLCBFeGNlbCwgUG93ZXJQb2ludCwgb3IgVGV4dCknKVxuICAgICAgICByZXR1cm5cbiAgICAgIH1cbiAgICAgIGlmIChmaWxlLnNpemUgPiAxMCAqIDEwMjQgKiAxMDI0KSB7IC8vIDEwTUIgbGltaXRcbiAgICAgICAgYWxlcnQoJ0ZpbGUgc2l6ZSBtdXN0IGJlIGxlc3MgdGhhbiAxME1CJylcbiAgICAgICAgcmV0dXJuXG4gICAgICB9XG4gICAgICBzZXRTZWxlY3RlZEZpbGUoZmlsZSlcbiAgICB9XG4gIH1cblxuICBjb25zdCBvblN1Ym1pdCA9IGFzeW5jIChkYXRhOiBUcmFuc2xhdGlvblJlcXVlc3RJbnB1dCkgPT4ge1xuICAgIHNldElzU3VibWl0dGluZyh0cnVlKVxuICAgIHNldFN1Ym1pdFN0YXR1cygnaWRsZScpXG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgZm9ybURhdGEgPSBuZXcgRm9ybURhdGEoKVxuICAgICAgXG4gICAgICAvLyBBZGQgZm9ybSBmaWVsZHNcbiAgICAgIE9iamVjdC5lbnRyaWVzKGRhdGEpLmZvckVhY2goKFtrZXksIHZhbHVlXSkgPT4ge1xuICAgICAgICBpZiAodmFsdWUgIT09IHVuZGVmaW5lZCAmJiB2YWx1ZSAhPT0gJycpIHtcbiAgICAgICAgICBmb3JtRGF0YS5hcHBlbmQoa2V5LCB2YWx1ZSlcbiAgICAgICAgfVxuICAgICAgfSlcblxuICAgICAgLy8gQWRkIGZpbGUgaWYgc2VsZWN0ZWRcbiAgICAgIGlmIChzZWxlY3RlZEZpbGUgJiYgdXNlRmlsZVVwbG9hZCkge1xuICAgICAgICBmb3JtRGF0YS5hcHBlbmQoJ2RvY3VtZW50RmlsZScsIHNlbGVjdGVkRmlsZSlcbiAgICAgIH1cblxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS90cmFuc2xhdGlvbi1yZXF1ZXN0Jywge1xuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgYm9keTogZm9ybURhdGEsXG4gICAgICB9KVxuXG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCByZXNwb25zZS5qc29uKClcblxuICAgICAgaWYgKHJlc3VsdC5zdWNjZXNzKSB7XG4gICAgICAgIHNldFN1Ym1pdFN0YXR1cygnc3VjY2VzcycpXG4gICAgICAgIHJlc2V0KClcbiAgICAgICAgc2V0U2VsZWN0ZWRGaWxlKG51bGwpXG4gICAgICB9IGVsc2Uge1xuICAgICAgICBzZXRTdWJtaXRTdGF0dXMoJ2Vycm9yJylcbiAgICAgICAgY29uc29sZS5lcnJvcignU3VibWlzc2lvbiBlcnJvcjonLCByZXN1bHQuZXJyb3IpXG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHNldFN1Ym1pdFN0YXR1cygnZXJyb3InKVxuICAgICAgY29uc29sZS5lcnJvcignU3VibWlzc2lvbiBlcnJvcjonLCBlcnJvcilcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNTdWJtaXR0aW5nKGZhbHNlKVxuICAgIH1cbiAgfVxuXG4gIGlmIChzdWJtaXRTdGF0dXMgPT09ICdzdWNjZXNzJykge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtMnhsIHNoYWRvdy1sZyBwLTggdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgPENoZWNrQ2lyY2xlIGNsYXNzTmFtZT1cImgtMTYgdy0xNiB0ZXh0LWdyZWVuLTUwMCBteC1hdXRvIG1iLTRcIiAvPlxuICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgbWItMlwiPlJlcXVlc3QgU3VibWl0dGVkIFN1Y2Nlc3NmdWxseSE8L2gzPlxuICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIG1iLTZcIj5cbiAgICAgICAgICBUaGFuayB5b3UgZm9yIHlvdXIgdHJhbnNsYXRpb24gcmVxdWVzdC4gV2UnbGwgcmV2aWV3IHlvdXIgcmVxdWlyZW1lbnRzIGFuZCBnZXQgYmFjayB0byB5b3Ugd2l0aGluIDI0IGhvdXJzLlxuICAgICAgICA8L3A+XG4gICAgICAgIDxidXR0b25cbiAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTdWJtaXRTdGF0dXMoJ2lkbGUnKX1cbiAgICAgICAgICBjbGFzc05hbWU9XCJiZy1wcmltYXJ5LTYwMCBob3ZlcjpiZy1wcmltYXJ5LTcwMCB0ZXh0LXdoaXRlIHB4LTYgcHktMiByb3VuZGVkLWxnIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgPlxuICAgICAgICAgIFN1Ym1pdCBBbm90aGVyIFJlcXVlc3RcbiAgICAgICAgPC9idXR0b24+XG4gICAgICA8L2Rpdj5cbiAgICApXG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC0yeGwgc2hhZG93LWxnIHAtOFwiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi04XCI+XG4gICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMCBtYi0yXCI+UmVxdWVzdCBhIFRyYW5zbGF0aW9uIFF1b3RlPC9oMz5cbiAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgIEZpbGwgb3V0IHRoZSBmb3JtIGJlbG93IGFuZCB3ZSdsbCBwcm92aWRlIHlvdSB3aXRoIGEgZGV0YWlsZWQgcXVvdGUgZm9yIHlvdXIgdHJhbnNsYXRpb24gcHJvamVjdC5cbiAgICAgICAgPC9wPlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHtzdWJtaXRTdGF0dXMgPT09ICdlcnJvcicgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTYgYmctcmVkLTUwIGJvcmRlciBib3JkZXItcmVkLTIwMCByb3VuZGVkLWxnIHAtNCBmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgIDxBbGVydENpcmNsZSBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtcmVkLTUwMCBtci0yXCIgLz5cbiAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXJlZC03MDBcIj5cbiAgICAgICAgICAgIFRoZXJlIHdhcyBhbiBlcnJvciBzdWJtaXR0aW5nIHlvdXIgcmVxdWVzdC4gUGxlYXNlIHRyeSBhZ2Fpbi5cbiAgICAgICAgICA8L3NwYW4+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cblxuICAgICAgPGZvcm0gb25TdWJtaXQ9e2hhbmRsZVN1Ym1pdChvblN1Ym1pdCl9IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgICB7LyogUGVyc29uYWwgSW5mb3JtYXRpb24gKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtNlwiPlxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8bGFiZWwgaHRtbEZvcj1cImZ1bGxOYW1lXCIgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgRnVsbCBOYW1lICpcbiAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgey4uLnJlZ2lzdGVyKCdmdWxsTmFtZScpfVxuICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgIGlkPVwiZnVsbE5hbWVcIlxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgICAgICAgICd3LWZ1bGwgcHgtNCBweS0zIGJvcmRlciByb3VuZGVkLWxnIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLXByaW1hcnktNTAwIGZvY3VzOmJvcmRlci1wcmltYXJ5LTUwMCB0cmFuc2l0aW9uLWNvbG9ycycsXG4gICAgICAgICAgICAgICAgZXJyb3JzLmZ1bGxOYW1lID8gJ2JvcmRlci1yZWQtMzAwJyA6ICdib3JkZXItZ3JheS0zMDAnXG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRW50ZXIgeW91ciBmdWxsIG5hbWVcIlxuICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIHtlcnJvcnMuZnVsbE5hbWUgJiYgKFxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtdC0xIHRleHQtc20gdGV4dC1yZWQtNjAwXCI+e2Vycm9ycy5mdWxsTmFtZS5tZXNzYWdlfTwvcD5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPGxhYmVsIGh0bWxGb3I9XCJjb21wYW55TmFtZVwiIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0yXCI+XG4gICAgICAgICAgICAgIENvbXBhbnkgTmFtZVxuICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICB7Li4ucmVnaXN0ZXIoJ2NvbXBhbnlOYW1lJyl9XG4gICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgaWQ9XCJjb21wYW55TmFtZVwiXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC00IHB5LTMgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLWxnIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLXByaW1hcnktNTAwIGZvY3VzOmJvcmRlci1wcmltYXJ5LTUwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRW50ZXIgeW91ciBjb21wYW55IG5hbWUgKG9wdGlvbmFsKVwiXG4gICAgICAgICAgICAvPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogRW1haWwgKi99XG4gICAgICAgIDxkaXY+XG4gICAgICAgICAgPGxhYmVsIGh0bWxGb3I9XCJlbWFpbFwiIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0yXCI+XG4gICAgICAgICAgICBFbWFpbCBBZGRyZXNzICpcbiAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgey4uLnJlZ2lzdGVyKCdlbWFpbCcpfVxuICAgICAgICAgICAgdHlwZT1cImVtYWlsXCJcbiAgICAgICAgICAgIGlkPVwiZW1haWxcIlxuICAgICAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgICAgJ3ctZnVsbCBweC00IHB5LTMgYm9yZGVyIHJvdW5kZWQtbGcgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctcHJpbWFyeS01MDAgZm9jdXM6Ym9yZGVyLXByaW1hcnktNTAwIHRyYW5zaXRpb24tY29sb3JzJyxcbiAgICAgICAgICAgICAgZXJyb3JzLmVtYWlsID8gJ2JvcmRlci1yZWQtMzAwJyA6ICdib3JkZXItZ3JheS0zMDAnXG4gICAgICAgICAgICApfVxuICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFbnRlciB5b3VyIGVtYWlsIGFkZHJlc3NcIlxuICAgICAgICAgIC8+XG4gICAgICAgICAge2Vycm9ycy5lbWFpbCAmJiAoXG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtdC0xIHRleHQtc20gdGV4dC1yZWQtNjAwXCI+e2Vycm9ycy5lbWFpbC5tZXNzYWdlfTwvcD5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogTGFuZ3VhZ2VzICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTZcIj5cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPGxhYmVsIGh0bWxGb3I9XCJzb3VyY2VMYW5ndWFnZVwiIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0yXCI+XG4gICAgICAgICAgICAgIFNvdXJjZSBMYW5ndWFnZSAqXG4gICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgPHNlbGVjdFxuICAgICAgICAgICAgICB7Li4ucmVnaXN0ZXIoJ3NvdXJjZUxhbmd1YWdlJyl9XG4gICAgICAgICAgICAgIGlkPVwic291cmNlTGFuZ3VhZ2VcIlxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgICAgICAgICd3LWZ1bGwgcHgtNCBweS0zIGJvcmRlciByb3VuZGVkLWxnIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLXByaW1hcnktNTAwIGZvY3VzOmJvcmRlci1wcmltYXJ5LTUwMCB0cmFuc2l0aW9uLWNvbG9ycycsXG4gICAgICAgICAgICAgICAgZXJyb3JzLnNvdXJjZUxhbmd1YWdlID8gJ2JvcmRlci1yZWQtMzAwJyA6ICdib3JkZXItZ3JheS0zMDAnXG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJcIj5TZWxlY3Qgc291cmNlIGxhbmd1YWdlPC9vcHRpb24+XG4gICAgICAgICAgICAgIHtMQU5HVUFHRVMubWFwKChsYW5nKSA9PiAoXG4gICAgICAgICAgICAgICAgPG9wdGlvbiBrZXk9e2xhbmcuY29kZX0gdmFsdWU9e2xhbmcubmFtZX0+XG4gICAgICAgICAgICAgICAgICB7bGFuZy5uYW1lfVxuICAgICAgICAgICAgICAgIDwvb3B0aW9uPlxuICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgICAge2Vycm9ycy5zb3VyY2VMYW5ndWFnZSAmJiAoXG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm10LTEgdGV4dC1zbSB0ZXh0LXJlZC02MDBcIj57ZXJyb3JzLnNvdXJjZUxhbmd1YWdlLm1lc3NhZ2V9PC9wPlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8bGFiZWwgaHRtbEZvcj1cInRhcmdldExhbmd1YWdlXCIgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgVGFyZ2V0IExhbmd1YWdlICpcbiAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgIHsuLi5yZWdpc3RlcigndGFyZ2V0TGFuZ3VhZ2UnKX1cbiAgICAgICAgICAgICAgaWQ9XCJ0YXJnZXRMYW5ndWFnZVwiXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgICAgICAgJ3ctZnVsbCBweC00IHB5LTMgYm9yZGVyIHJvdW5kZWQtbGcgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctcHJpbWFyeS01MDAgZm9jdXM6Ym9yZGVyLXByaW1hcnktNTAwIHRyYW5zaXRpb24tY29sb3JzJyxcbiAgICAgICAgICAgICAgICBlcnJvcnMudGFyZ2V0TGFuZ3VhZ2UgPyAnYm9yZGVyLXJlZC0zMDAnIDogJ2JvcmRlci1ncmF5LTMwMCdcbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIlwiPlNlbGVjdCB0YXJnZXQgbGFuZ3VhZ2U8L29wdGlvbj5cbiAgICAgICAgICAgICAge0xBTkdVQUdFUy5maWx0ZXIobGFuZyA9PiBsYW5nLm5hbWUgIT09IHNvdXJjZUxhbmd1YWdlKS5tYXAoKGxhbmcpID0+IChcbiAgICAgICAgICAgICAgICA8b3B0aW9uIGtleT17bGFuZy5jb2RlfSB2YWx1ZT17bGFuZy5uYW1lfT5cbiAgICAgICAgICAgICAgICAgIHtsYW5nLm5hbWV9XG4gICAgICAgICAgICAgICAgPC9vcHRpb24+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC9zZWxlY3Q+XG4gICAgICAgICAgICB7ZXJyb3JzLnRhcmdldExhbmd1YWdlICYmIChcbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwibXQtMSB0ZXh0LXNtIHRleHQtcmVkLTYwMFwiPntlcnJvcnMudGFyZ2V0TGFuZ3VhZ2UubWVzc2FnZX08L3A+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogRGVhZGxpbmUgKi99XG4gICAgICAgIDxkaXY+XG4gICAgICAgICAgPGxhYmVsIGh0bWxGb3I9XCJkZWFkbGluZVwiIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0yXCI+XG4gICAgICAgICAgICBEZWFkbGluZSAqXG4gICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgIHsuLi5yZWdpc3RlcignZGVhZGxpbmUnKX1cbiAgICAgICAgICAgIHR5cGU9XCJkYXRlXCJcbiAgICAgICAgICAgIGlkPVwiZGVhZGxpbmVcIlxuICAgICAgICAgICAgbWluPXtnZXRUb21vcnJvd0RhdGUoKX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgICAgICd3LWZ1bGwgcHgtNCBweS0zIGJvcmRlciByb3VuZGVkLWxnIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLXByaW1hcnktNTAwIGZvY3VzOmJvcmRlci1wcmltYXJ5LTUwMCB0cmFuc2l0aW9uLWNvbG9ycycsXG4gICAgICAgICAgICAgIGVycm9ycy5kZWFkbGluZSA/ICdib3JkZXItcmVkLTMwMCcgOiAnYm9yZGVyLWdyYXktMzAwJ1xuICAgICAgICAgICAgKX1cbiAgICAgICAgICAvPlxuICAgICAgICAgIHtlcnJvcnMuZGVhZGxpbmUgJiYgKFxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwibXQtMSB0ZXh0LXNtIHRleHQtcmVkLTYwMFwiPntlcnJvcnMuZGVhZGxpbmUubWVzc2FnZX08L3A+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIERvY3VtZW50IFVwbG9hZC9VUkwgKi99XG4gICAgICAgIDxkaXY+XG4gICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0yXCI+XG4gICAgICAgICAgICBEb2N1bWVudFxuICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi00XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC00XCI+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRVc2VGaWxlVXBsb2FkKHRydWUpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgICAgICAgICAncHgtNCBweS0yIHJvdW5kZWQtbGcgZm9udC1tZWRpdW0gdHJhbnNpdGlvbi1jb2xvcnMnLFxuICAgICAgICAgICAgICAgICAgdXNlRmlsZVVwbG9hZFxuICAgICAgICAgICAgICAgICAgICA/ICdiZy1wcmltYXJ5LTYwMCB0ZXh0LXdoaXRlJ1xuICAgICAgICAgICAgICAgICAgICA6ICdiZy1ncmF5LTEwMCB0ZXh0LWdyYXktNzAwIGhvdmVyOmJnLWdyYXktMjAwJ1xuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8VXBsb2FkIGNsYXNzTmFtZT1cImgtNCB3LTQgaW5saW5lIG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgIFVwbG9hZCBGaWxlXG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0VXNlRmlsZVVwbG9hZChmYWxzZSl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgICAgICAgICdweC00IHB5LTIgcm91bmRlZC1sZyBmb250LW1lZGl1bSB0cmFuc2l0aW9uLWNvbG9ycycsXG4gICAgICAgICAgICAgICAgICAhdXNlRmlsZVVwbG9hZFxuICAgICAgICAgICAgICAgICAgICA/ICdiZy1wcmltYXJ5LTYwMCB0ZXh0LXdoaXRlJ1xuICAgICAgICAgICAgICAgICAgICA6ICdiZy1ncmF5LTEwMCB0ZXh0LWdyYXktNzAwIGhvdmVyOmJnLWdyYXktMjAwJ1xuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8TGluayBjbGFzc05hbWU9XCJoLTQgdy00IGlubGluZSBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICBQcm92aWRlIFVSTFxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAge3VzZUZpbGVVcGxvYWQgPyAoXG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICB0eXBlPVwiZmlsZVwiXG4gICAgICAgICAgICAgICAgaWQ9XCJkb2N1bWVudEZpbGVcIlxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVGaWxlQ2hhbmdlfVxuICAgICAgICAgICAgICAgIGFjY2VwdD1cIi5wZGYsLmRvYywuZG9jeCwudHh0LC54bHMsLnhsc3gsLnBwdCwucHB0eFwiXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaGlkZGVuXCJcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPGxhYmVsXG4gICAgICAgICAgICAgICAgaHRtbEZvcj1cImRvY3VtZW50RmlsZVwiXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGJvcmRlci0yIGJvcmRlci1kYXNoZWQgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbGcgcC02IHRleHQtY2VudGVyIGN1cnNvci1wb2ludGVyIGhvdmVyOmJvcmRlci1wcmltYXJ5LTQwMCB0cmFuc2l0aW9uLWNvbG9ycyBibG9ja1wiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICB7c2VsZWN0ZWRGaWxlID8gKFxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwXCI+e3NlbGVjdGVkRmlsZS5uYW1lfTwvcD5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+e2Zvcm1hdEZpbGVTaXplKHNlbGVjdGVkRmlsZS5zaXplKX08L3A+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPFVwbG9hZCBjbGFzc05hbWU9XCJoLTggdy04IHRleHQtZ3JheS00MDAgbXgtYXV0byBtYi0yXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgQ2xpY2sgdG8gdXBsb2FkIG9yIGRyYWcgYW5kIGRyb3BcbiAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDAgbXQtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgIFBERiwgV29yZCwgRXhjZWwsIFBvd2VyUG9pbnQsIG9yIFRleHQgZmlsZXMgKG1heCAxME1CKVxuICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKSA6IChcbiAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICB7Li4ucmVnaXN0ZXIoJ2RvY3VtZW50VXJsJyl9XG4gICAgICAgICAgICAgIHR5cGU9XCJ1cmxcIlxuICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cImh0dHBzOi8vZXhhbXBsZS5jb20vZG9jdW1lbnQucGRmXCJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTQgcHktMyBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbGcgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctcHJpbWFyeS01MDAgZm9jdXM6Ym9yZGVyLXByaW1hcnktNTAwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIEFkZGl0aW9uYWwgSW5mb3JtYXRpb24gKi99XG4gICAgICAgIDxkaXY+XG4gICAgICAgICAgPGxhYmVsIGh0bWxGb3I9XCJhZGRpdGlvbmFsSW5mb1wiIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0yXCI+XG4gICAgICAgICAgICBBZGRpdGlvbmFsIEluZm9ybWF0aW9uXG4gICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICA8dGV4dGFyZWFcbiAgICAgICAgICAgIHsuLi5yZWdpc3RlcignYWRkaXRpb25hbEluZm8nKX1cbiAgICAgICAgICAgIGlkPVwiYWRkaXRpb25hbEluZm9cIlxuICAgICAgICAgICAgcm93cz17NH1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC00IHB5LTMgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLWxnIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLXByaW1hcnktNTAwIGZvY3VzOmJvcmRlci1wcmltYXJ5LTUwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlBsZWFzZSBwcm92aWRlIGFueSBhZGRpdGlvbmFsIGRldGFpbHMgYWJvdXQgeW91ciB0cmFuc2xhdGlvbiBwcm9qZWN0Li4uXCJcbiAgICAgICAgICAvPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogU3VibWl0IEJ1dHRvbiAqL31cbiAgICAgICAgPGJ1dHRvblxuICAgICAgICAgIHR5cGU9XCJzdWJtaXRcIlxuICAgICAgICAgIGRpc2FibGVkPXtpc1N1Ym1pdHRpbmd9XG4gICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGJnLXByaW1hcnktNjAwIGhvdmVyOmJnLXByaW1hcnktNzAwIGRpc2FibGVkOmJnLXByaW1hcnktNDAwIHRleHQtd2hpdGUgZm9udC1tZWRpdW0gcHktMyBweC02IHJvdW5kZWQtbGcgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCJcbiAgICAgICAgPlxuICAgICAgICAgIHtpc1N1Ym1pdHRpbmcgPyAoXG4gICAgICAgICAgICA8PlxuICAgICAgICAgICAgICA8TG9hZGVyMiBjbGFzc05hbWU9XCJoLTUgdy01IGFuaW1hdGUtc3BpbiBtci0yXCIgLz5cbiAgICAgICAgICAgICAgU3VibWl0dGluZyBSZXF1ZXN0Li4uXG4gICAgICAgICAgICA8Lz5cbiAgICAgICAgICApIDogKFxuICAgICAgICAgICAgJ1N1Ym1pdCBUcmFuc2xhdGlvbiBSZXF1ZXN0J1xuICAgICAgICAgICl9XG4gICAgICAgIDwvYnV0dG9uPlxuICAgICAgPC9mb3JtPlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VGb3JtIiwiem9kUmVzb2x2ZXIiLCJVcGxvYWQiLCJMaW5rIiwiQ2hlY2tDaXJjbGUiLCJBbGVydENpcmNsZSIsIkxvYWRlcjIiLCJ0cmFuc2xhdGlvblJlcXVlc3RTY2hlbWEiLCJMQU5HVUFHRVMiLCJnZXRUb21vcnJvd0RhdGUiLCJmb3JtYXRGaWxlU2l6ZSIsImlzVmFsaWRGaWxlVHlwZSIsImNuIiwiVHJhbnNsYXRpb25Gb3JtIiwiaXNTdWJtaXR0aW5nIiwic2V0SXNTdWJtaXR0aW5nIiwic3VibWl0U3RhdHVzIiwic2V0U3VibWl0U3RhdHVzIiwic2VsZWN0ZWRGaWxlIiwic2V0U2VsZWN0ZWRGaWxlIiwidXNlRmlsZVVwbG9hZCIsInNldFVzZUZpbGVVcGxvYWQiLCJyZWdpc3RlciIsImhhbmRsZVN1Ym1pdCIsImZvcm1TdGF0ZSIsImVycm9ycyIsInJlc2V0Iiwid2F0Y2giLCJyZXNvbHZlciIsInNvdXJjZUxhbmd1YWdlIiwidGFyZ2V0TGFuZ3VhZ2UiLCJoYW5kbGVGaWxlQ2hhbmdlIiwiZXZlbnQiLCJmaWxlIiwidGFyZ2V0IiwiZmlsZXMiLCJhbGVydCIsInNpemUiLCJvblN1Ym1pdCIsImRhdGEiLCJmb3JtRGF0YSIsIkZvcm1EYXRhIiwiT2JqZWN0IiwiZW50cmllcyIsImZvckVhY2giLCJrZXkiLCJ2YWx1ZSIsInVuZGVmaW5lZCIsImFwcGVuZCIsInJlc3BvbnNlIiwiZmV0Y2giLCJtZXRob2QiLCJib2R5IiwicmVzdWx0IiwianNvbiIsInN1Y2Nlc3MiLCJjb25zb2xlIiwiZXJyb3IiLCJkaXYiLCJjbGFzc05hbWUiLCJoMyIsInAiLCJidXR0b24iLCJvbkNsaWNrIiwic3BhbiIsImZvcm0iLCJsYWJlbCIsImh0bWxGb3IiLCJpbnB1dCIsInR5cGUiLCJpZCIsImZ1bGxOYW1lIiwicGxhY2Vob2xkZXIiLCJtZXNzYWdlIiwiZW1haWwiLCJzZWxlY3QiLCJvcHRpb24iLCJtYXAiLCJsYW5nIiwibmFtZSIsImNvZGUiLCJmaWx0ZXIiLCJtaW4iLCJkZWFkbGluZSIsIm9uQ2hhbmdlIiwiYWNjZXB0IiwidGV4dGFyZWEiLCJyb3dzIiwiZGlzYWJsZWQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/TranslationForm.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/constants.ts":
/*!**************************!*\
  !*** ./lib/constants.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   INDUSTRIES: () => (/* binding */ INDUSTRIES),\n/* harmony export */   LANGUAGES: () => (/* binding */ LANGUAGES),\n/* harmony export */   SERVICES: () => (/* binding */ SERVICES)\n/* harmony export */ });\nconst LANGUAGES = [\n    {\n        code: \"en\",\n        name: \"English\"\n    },\n    {\n        code: \"es\",\n        name: \"Spanish\"\n    },\n    {\n        code: \"fr\",\n        name: \"French\"\n    },\n    {\n        code: \"de\",\n        name: \"German\"\n    },\n    {\n        code: \"it\",\n        name: \"Italian\"\n    },\n    {\n        code: \"pt\",\n        name: \"Portuguese\"\n    },\n    {\n        code: \"ru\",\n        name: \"Russian\"\n    },\n    {\n        code: \"zh\",\n        name: \"Chinese (Mandarin)\"\n    },\n    {\n        code: \"ja\",\n        name: \"Japanese\"\n    },\n    {\n        code: \"ko\",\n        name: \"Korean\"\n    },\n    {\n        code: \"ar\",\n        name: \"Arabic\"\n    },\n    {\n        code: \"hi\",\n        name: \"Hindi\"\n    },\n    {\n        code: \"tr\",\n        name: \"Turkish\"\n    },\n    {\n        code: \"pl\",\n        name: \"Polish\"\n    },\n    {\n        code: \"nl\",\n        name: \"Dutch\"\n    },\n    {\n        code: \"sv\",\n        name: \"Swedish\"\n    },\n    {\n        code: \"da\",\n        name: \"Danish\"\n    },\n    {\n        code: \"no\",\n        name: \"Norwegian\"\n    },\n    {\n        code: \"fi\",\n        name: \"Finnish\"\n    },\n    {\n        code: \"he\",\n        name: \"Hebrew\"\n    }\n];\nconst SERVICES = [\n    {\n        id: \"document-translation\",\n        title: \"Document Translation\",\n        description: \"Professional translation of documents, contracts, manuals, and reports with accuracy and cultural sensitivity.\",\n        icon: \"FileText\"\n    },\n    {\n        id: \"website-localization\",\n        title: \"Website Localization\",\n        description: \"Complete website localization including content translation, cultural adaptation, and technical implementation.\",\n        icon: \"Globe\"\n    },\n    {\n        id: \"interpretation-services\",\n        title: \"Interpretation Services\",\n        description: \"Real-time interpretation for meetings, conferences, and events with certified professional interpreters.\",\n        icon: \"Mic\"\n    },\n    {\n        id: \"technical-translation\",\n        title: \"Technical Translation\",\n        description: \"Specialized translation for technical documentation, software, and engineering materials.\",\n        icon: \"Settings\"\n    },\n    {\n        id: \"legal-translation\",\n        title: \"Legal Translation\",\n        description: \"Certified legal document translation by qualified legal translators with expertise in international law.\",\n        icon: \"Scale\"\n    }\n];\nconst INDUSTRIES = [\n    {\n        id: \"healthcare\",\n        title: \"Healthcare\",\n        description: \"Medical documents, clinical trials, pharmaceutical documentation, and patient materials.\",\n        icon: \"Heart\"\n    },\n    {\n        id: \"legal\",\n        title: \"Legal\",\n        description: \"Contracts, court documents, legal correspondence, and regulatory compliance materials.\",\n        icon: \"Gavel\"\n    },\n    {\n        id: \"technology\",\n        title: \"Technology\",\n        description: \"Software documentation, user manuals, technical specifications, and IT content.\",\n        icon: \"Laptop\"\n    },\n    {\n        id: \"finance\",\n        title: \"Finance\",\n        description: \"Financial reports, banking documents, investment materials, and regulatory filings.\",\n        icon: \"DollarSign\"\n    },\n    {\n        id: \"education\",\n        title: \"Education\",\n        description: \"Academic papers, educational materials, course content, and institutional documents.\",\n        icon: \"GraduationCap\"\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/constants.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   formatFileSize: () => (/* binding */ formatFileSize),\n/* harmony export */   getTomorrowDate: () => (/* binding */ getTomorrowDate),\n/* harmony export */   isValidFileType: () => (/* binding */ isValidFileType)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatFileSize(bytes) {\n    if (bytes === 0) return \"0 Bytes\";\n    const k = 1024;\n    const sizes = [\n        \"Bytes\",\n        \"KB\",\n        \"MB\",\n        \"GB\"\n    ];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n}\nfunction isValidFileType(file) {\n    const allowedTypes = [\n        \"application/pdf\",\n        \"application/msword\",\n        \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\",\n        \"text/plain\",\n        \"application/vnd.ms-excel\",\n        \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\",\n        \"application/vnd.ms-powerpoint\",\n        \"application/vnd.openxmlformats-officedocument.presentationml.presentation\"\n    ];\n    return allowedTypes.includes(file.type);\n}\nfunction getTomorrowDate() {\n    const tomorrow = new Date();\n    tomorrow.setDate(tomorrow.getDate() + 1);\n    return tomorrow.toISOString().split(\"T\")[0];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./lib/validation.ts":
/*!***************************!*\
  !*** ./lib/validation.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   translationRequestSchema: () => (/* binding */ translationRequestSchema)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(ssr)/./node_modules/zod/dist/esm/index.js\");\n\nconst translationRequestSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    fullName: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(2, \"Full name must be at least 2 characters\"),\n    companyName: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    email: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().email(\"Please enter a valid email address\"),\n    sourceLanguage: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Please select a source language\"),\n    targetLanguage: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Please select a target language\"),\n    deadline: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().refine((date)=>{\n        const selectedDate = new Date(date);\n        const tomorrow = new Date();\n        tomorrow.setDate(tomorrow.getDate() + 1);\n        tomorrow.setHours(0, 0, 0, 0);\n        return selectedDate >= tomorrow;\n    }, \"Deadline must be at least tomorrow\"),\n    documentUrl: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().url().optional().or(zod__WEBPACK_IMPORTED_MODULE_0__.z.literal(\"\")),\n    additionalInfo: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional()\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/validation.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"b492f2b54b5a\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9nbG9iYWxpbmctd2Vic2l0ZS8uL2FwcC9nbG9iYWxzLmNzcz84ZDNkIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiYjQ5MmYyYjU0YjVhXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"Globaling - Professional Translation Services\",\n    description: \"Professional translation services for documents, websites, and more. Expert translators for healthcare, legal, technology, finance, and education industries.\",\n    keywords: \"translation, localization, interpretation, document translation, website localization, professional translators\",\n    authors: [\n        {\n            name: \"Globaling Team\"\n        }\n    ],\n    openGraph: {\n        title: \"Globaling - Professional Translation Services\",\n        description: \"Professional translation services for documents, websites, and more.\",\n        type: \"website\",\n        locale: \"en_US\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\",\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/layout.tsx\",\n                lineNumber: 28,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/layout.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/layout.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Header */ \"(rsc)/./components/Header.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Footer */ \"(rsc)/./components/Footer.tsx\");\n/* harmony import */ var _components_ServicesSection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ServicesSection */ \"(rsc)/./components/ServicesSection.tsx\");\n/* harmony import */ var _components_IndustriesSection__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/IndustriesSection */ \"(rsc)/./components/IndustriesSection.tsx\");\n/* harmony import */ var _components_TranslationForm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/TranslationForm */ \"(rsc)/./components/TranslationForm.tsx\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_Clock_Globe_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,Clock,Globe,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_Clock_Globe_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,Clock,Globe,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_Clock_Globe_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,Clock,Globe,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_Clock_Globe_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,Clock,Globe,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_Clock_Globe_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,Clock,Globe,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n\n\n\n\n\n\n\nfunction HomePage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative py-20 lg:py-32 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-black/20\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center mb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_Clock_Globe_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-16 w-16 text-primary-200\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\",\n                                        lineNumber: 21,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\",\n                                    lineNumber: 20,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl md:text-6xl font-bold mb-6\",\n                                    children: \"Professional Translation Services\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\",\n                                    lineNumber: 23,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl md:text-2xl mb-8 text-primary-100 max-w-3xl mx-auto\",\n                                    children: \"Breaking language barriers with accurate, culturally-sensitive translations for businesses and individuals worldwide.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\",\n                                    lineNumber: 26,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                const element = document.getElementById(\"request-form\");\n                                                if (element) {\n                                                    element.scrollIntoView({\n                                                        behavior: \"smooth\"\n                                                    });\n                                                }\n                                            },\n                                            className: \"bg-white text-primary-600 hover:bg-gray-100 px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-200\",\n                                            children: \"Get Free Quote\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\",\n                                            lineNumber: 31,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                const element = document.getElementById(\"services\");\n                                                if (element) {\n                                                    element.scrollIntoView({\n                                                        behavior: \"smooth\"\n                                                    });\n                                                }\n                                            },\n                                            className: \"border-2 border-white text-white hover:bg-white hover:text-primary-600 px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-200\",\n                                            children: \"Our Services\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\",\n                                            lineNumber: 42,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\",\n                                    lineNumber: 30,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                                    children: \"Why Choose Globaling?\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                                    children: \"We combine human expertise with modern technology to deliver exceptional translation services.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-primary-100 rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_Clock_Globe_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-8 w-8 text-primary-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\",\n                                                lineNumber: 73,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-gray-900 mb-2\",\n                                            children: \"99% Accuracy\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Our certified translators ensure the highest level of accuracy in every project.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-primary-100 rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_Clock_Globe_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-8 w-8 text-primary-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-gray-900 mb-2\",\n                                            children: \"Fast Delivery\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Quick turnaround times without compromising on quality or accuracy.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-primary-100 rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_Clock_Globe_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-8 w-8 text-primary-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-gray-900 mb-2\",\n                                            children: \"Expert Team\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Native speakers and subject matter experts across various industries.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-primary-100 rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_Clock_Globe_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-8 w-8 text-primary-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-gray-900 mb-2\",\n                                            children: \"Certified Quality\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"ISO-certified processes and quality assurance for professional standards.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ServicesSection__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\",\n                lineNumber: 114,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_IndustriesSection__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"request-form\",\n                className: \"py-16 bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TranslationForm__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\",\n                lineNumber: 118,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 bg-gradient-to-r from-primary-600 to-primary-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-white mb-4\",\n                            children: \"Ready to Break Language Barriers?\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-primary-100 mb-8 max-w-2xl mx-auto\",\n                            children: \"Join thousands of satisfied clients who trust Globaling for their translation needs.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                const element = document.getElementById(\"request-form\");\n                                if (element) {\n                                    element.scrollIntoView({\n                                        behavior: \"smooth\"\n                                    });\n                                }\n                            },\n                            className: \"bg-white text-primary-600 hover:bg-gray-100 px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-200\",\n                            children: \"Start Your Project Today\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\",\n                lineNumber: 125,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\",\n                lineNumber: 147,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/SideProjects/globaling/app/page.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./components/Footer.tsx":
/*!*******************************!*\
  !*** ./components/Footer.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Globe_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Mail,MapPin,Phone!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Mail,MapPin,Phone!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Mail,MapPin,Phone!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Mail,MapPin,Phone!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n\n\nfunction Footer() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-gray-900 text-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-span-1 md:col-span-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            className: \"h-8 w-8 text-primary-400\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Footer.tsx\",\n                                            lineNumber: 11,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: \"Globaling\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Footer.tsx\",\n                                            lineNumber: 12,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Footer.tsx\",\n                                    lineNumber: 10,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 mb-6 max-w-md\",\n                                    children: \"Professional translation services connecting businesses and individuals across language barriers. We provide accurate, culturally-sensitive translations for all your communication needs.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Footer.tsx\",\n                                    lineNumber: 14,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 text-gray-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Footer.tsx\",\n                                                    lineNumber: 20,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"<EMAIL>\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Footer.tsx\",\n                                                    lineNumber: 21,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Footer.tsx\",\n                                            lineNumber: 19,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 text-gray-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Footer.tsx\",\n                                                    lineNumber: 24,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"+****************\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Footer.tsx\",\n                                                    lineNumber: 25,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Footer.tsx\",\n                                            lineNumber: 23,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 text-gray-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Footer.tsx\",\n                                                    lineNumber: 28,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"123 Translation Ave, Global City, GC 12345\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Footer.tsx\",\n                                                    lineNumber: 29,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Footer.tsx\",\n                                            lineNumber: 27,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Footer.tsx\",\n                                    lineNumber: 18,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Footer.tsx\",\n                            lineNumber: 9,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"Services\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Footer.tsx\",\n                                    lineNumber: 36,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2 text-gray-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Document Translation\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Footer.tsx\",\n                                            lineNumber: 38,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Website Localization\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Footer.tsx\",\n                                            lineNumber: 39,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Interpretation Services\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Footer.tsx\",\n                                            lineNumber: 40,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Technical Translation\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Footer.tsx\",\n                                            lineNumber: 41,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Legal Translation\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Footer.tsx\",\n                                            lineNumber: 42,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Footer.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Footer.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"Industries\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Footer.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2 text-gray-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Healthcare\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Footer.tsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Legal\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Footer.tsx\",\n                                            lineNumber: 51,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Technology\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Footer.tsx\",\n                                            lineNumber: 52,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Finance\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Footer.tsx\",\n                                            lineNumber: 53,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Education\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Footer.tsx\",\n                                            lineNumber: 54,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Footer.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Footer.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Footer.tsx\",\n                    lineNumber: 7,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"\\xa9 2024 Globaling. All rights reserved.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Footer.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Footer.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Footer.tsx\",\n            lineNumber: 6,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/Footer.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./components/Footer.tsx\n");

/***/ }),

/***/ "(rsc)/./components/Header.tsx":
/*!*******************************!*\
  !*** ./components/Header.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/SideProjects/globaling/components/Header.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./components/IndustriesSection.tsx":
/*!******************************************!*\
  !*** ./components/IndustriesSection.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ IndustriesSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_DollarSign_Gavel_GraduationCap_Heart_Laptop_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,Gavel,GraduationCap,Heart,Laptop!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_Gavel_GraduationCap_Heart_Laptop_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,Gavel,GraduationCap,Heart,Laptop!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/gavel.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_Gavel_GraduationCap_Heart_Laptop_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,Gavel,GraduationCap,Heart,Laptop!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/laptop.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_Gavel_GraduationCap_Heart_Laptop_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,Gavel,GraduationCap,Heart,Laptop!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_Gavel_GraduationCap_Heart_Laptop_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,Gavel,GraduationCap,Heart,Laptop!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/constants */ \"(rsc)/./lib/constants.ts\");\n\n\n\nconst iconMap = {\n    Heart: _barrel_optimize_names_DollarSign_Gavel_GraduationCap_Heart_Laptop_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n    Gavel: _barrel_optimize_names_DollarSign_Gavel_GraduationCap_Heart_Laptop_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n    Laptop: _barrel_optimize_names_DollarSign_Gavel_GraduationCap_Heart_Laptop_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n    DollarSign: _barrel_optimize_names_DollarSign_Gavel_GraduationCap_Heart_Laptop_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n    GraduationCap: _barrel_optimize_names_DollarSign_Gavel_GraduationCap_Heart_Laptop_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n};\nfunction IndustriesSection() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"industries\",\n        className: \"py-16 bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                            children: \"Industries We Serve\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/IndustriesSection.tsx\",\n                            lineNumber: 17,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                            children: \"Our specialized translators have deep expertise across various industries, ensuring your content is translated with the right terminology and context.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/IndustriesSection.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/IndustriesSection.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                    children: _lib_constants__WEBPACK_IMPORTED_MODULE_1__.INDUSTRIES.map((industry, index)=>{\n                        const IconComponent = iconMap[industry.icon];\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-300 border border-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg p-3 mr-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                className: \"h-6 w-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/IndustriesSection.tsx\",\n                                                lineNumber: 37,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/IndustriesSection.tsx\",\n                                            lineNumber: 36,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-gray-900\",\n                                            children: industry.title\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/IndustriesSection.tsx\",\n                                            lineNumber: 39,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/IndustriesSection.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 leading-relaxed\",\n                                    children: industry.description\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/IndustriesSection.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, industry.id, true, {\n                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/IndustriesSection.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/IndustriesSection.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-12 bg-gradient-to-r from-primary-600 to-primary-700 rounded-2xl p-8 text-center text-white\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-bold mb-4\",\n                            children: \"Don't See Your Industry?\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/IndustriesSection.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg mb-6 opacity-90\",\n                            children: \"We work with clients across many other sectors. Contact us to discuss your specific translation needs.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/IndustriesSection.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                const element = document.getElementById(\"request-form\");\n                                if (element) {\n                                    element.scrollIntoView({\n                                        behavior: \"smooth\"\n                                    });\n                                }\n                            },\n                            className: \"bg-white text-primary-600 hover:bg-gray-100 px-8 py-3 rounded-lg font-medium text-lg transition-colors duration-200\",\n                            children: \"Get in Touch\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/IndustriesSection.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/IndustriesSection.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/IndustriesSection.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/IndustriesSection.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./components/IndustriesSection.tsx\n");

/***/ }),

/***/ "(rsc)/./components/ServicesSection.tsx":
/*!****************************************!*\
  !*** ./components/ServicesSection.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ServicesSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_FileText_Globe_Mic_Scale_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Globe,Mic,Scale,Settings!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_Globe_Mic_Scale_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Globe,Mic,Scale,Settings!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_Globe_Mic_Scale_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Globe,Mic,Scale,Settings!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_Globe_Mic_Scale_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Globe,Mic,Scale,Settings!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_Globe_Mic_Scale_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Globe,Mic,Scale,Settings!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/scale.js\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/constants */ \"(rsc)/./lib/constants.ts\");\n\n\n\nconst iconMap = {\n    FileText: _barrel_optimize_names_FileText_Globe_Mic_Scale_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n    Globe: _barrel_optimize_names_FileText_Globe_Mic_Scale_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n    Mic: _barrel_optimize_names_FileText_Globe_Mic_Scale_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n    Settings: _barrel_optimize_names_FileText_Globe_Mic_Scale_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n    Scale: _barrel_optimize_names_FileText_Globe_Mic_Scale_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n};\nfunction ServicesSection() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"services\",\n        className: \"py-16 bg-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                            children: \"Our Translation Services\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/ServicesSection.tsx\",\n                            lineNumber: 17,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                            children: \"We offer comprehensive translation solutions tailored to meet your specific needs, ensuring accuracy, cultural sensitivity, and timely delivery.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/ServicesSection.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/ServicesSection.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                    children: _lib_constants__WEBPACK_IMPORTED_MODULE_1__.SERVICES.map((service)=>{\n                        const IconComponent = iconMap[service.icon];\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 hover:shadow-lg transition-shadow duration-300\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-primary-600 rounded-lg p-3 mr-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                className: \"h-6 w-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/ServicesSection.tsx\",\n                                                lineNumber: 37,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/ServicesSection.tsx\",\n                                            lineNumber: 36,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-gray-900\",\n                                            children: service.title\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/ServicesSection.tsx\",\n                                            lineNumber: 39,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/ServicesSection.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 leading-relaxed\",\n                                    children: service.description\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/ServicesSection.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, service.id, true, {\n                            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/ServicesSection.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/ServicesSection.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-12 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>{\n                            const element = document.getElementById(\"request-form\");\n                            if (element) {\n                                element.scrollIntoView({\n                                    behavior: \"smooth\"\n                                });\n                            }\n                        },\n                        className: \"bg-primary-600 hover:bg-primary-700 text-white px-8 py-3 rounded-lg font-medium text-lg transition-colors duration-200\",\n                        children: \"Request a Quote\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/ServicesSection.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/ServicesSection.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/ServicesSection.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/SideProjects/globaling/components/ServicesSection.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./components/ServicesSection.tsx\n");

/***/ }),

/***/ "(rsc)/./components/TranslationForm.tsx":
/*!****************************************!*\
  !*** ./components/TranslationForm.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/SideProjects/globaling/components/TranslationForm.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./lib/constants.ts":
/*!**************************!*\
  !*** ./lib/constants.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   INDUSTRIES: () => (/* binding */ INDUSTRIES),\n/* harmony export */   LANGUAGES: () => (/* binding */ LANGUAGES),\n/* harmony export */   SERVICES: () => (/* binding */ SERVICES)\n/* harmony export */ });\nconst LANGUAGES = [\n    {\n        code: \"en\",\n        name: \"English\"\n    },\n    {\n        code: \"es\",\n        name: \"Spanish\"\n    },\n    {\n        code: \"fr\",\n        name: \"French\"\n    },\n    {\n        code: \"de\",\n        name: \"German\"\n    },\n    {\n        code: \"it\",\n        name: \"Italian\"\n    },\n    {\n        code: \"pt\",\n        name: \"Portuguese\"\n    },\n    {\n        code: \"ru\",\n        name: \"Russian\"\n    },\n    {\n        code: \"zh\",\n        name: \"Chinese (Mandarin)\"\n    },\n    {\n        code: \"ja\",\n        name: \"Japanese\"\n    },\n    {\n        code: \"ko\",\n        name: \"Korean\"\n    },\n    {\n        code: \"ar\",\n        name: \"Arabic\"\n    },\n    {\n        code: \"hi\",\n        name: \"Hindi\"\n    },\n    {\n        code: \"tr\",\n        name: \"Turkish\"\n    },\n    {\n        code: \"pl\",\n        name: \"Polish\"\n    },\n    {\n        code: \"nl\",\n        name: \"Dutch\"\n    },\n    {\n        code: \"sv\",\n        name: \"Swedish\"\n    },\n    {\n        code: \"da\",\n        name: \"Danish\"\n    },\n    {\n        code: \"no\",\n        name: \"Norwegian\"\n    },\n    {\n        code: \"fi\",\n        name: \"Finnish\"\n    },\n    {\n        code: \"he\",\n        name: \"Hebrew\"\n    }\n];\nconst SERVICES = [\n    {\n        id: \"document-translation\",\n        title: \"Document Translation\",\n        description: \"Professional translation of documents, contracts, manuals, and reports with accuracy and cultural sensitivity.\",\n        icon: \"FileText\"\n    },\n    {\n        id: \"website-localization\",\n        title: \"Website Localization\",\n        description: \"Complete website localization including content translation, cultural adaptation, and technical implementation.\",\n        icon: \"Globe\"\n    },\n    {\n        id: \"interpretation-services\",\n        title: \"Interpretation Services\",\n        description: \"Real-time interpretation for meetings, conferences, and events with certified professional interpreters.\",\n        icon: \"Mic\"\n    },\n    {\n        id: \"technical-translation\",\n        title: \"Technical Translation\",\n        description: \"Specialized translation for technical documentation, software, and engineering materials.\",\n        icon: \"Settings\"\n    },\n    {\n        id: \"legal-translation\",\n        title: \"Legal Translation\",\n        description: \"Certified legal document translation by qualified legal translators with expertise in international law.\",\n        icon: \"Scale\"\n    }\n];\nconst INDUSTRIES = [\n    {\n        id: \"healthcare\",\n        title: \"Healthcare\",\n        description: \"Medical documents, clinical trials, pharmaceutical documentation, and patient materials.\",\n        icon: \"Heart\"\n    },\n    {\n        id: \"legal\",\n        title: \"Legal\",\n        description: \"Contracts, court documents, legal correspondence, and regulatory compliance materials.\",\n        icon: \"Gavel\"\n    },\n    {\n        id: \"technology\",\n        title: \"Technology\",\n        description: \"Software documentation, user manuals, technical specifications, and IT content.\",\n        icon: \"Laptop\"\n    },\n    {\n        id: \"finance\",\n        title: \"Finance\",\n        description: \"Financial reports, banking documents, investment materials, and regulatory filings.\",\n        icon: \"DollarSign\"\n    },\n    {\n        id: \"education\",\n        title: \"Education\",\n        description: \"Academic papers, educational materials, course content, and institutional documents.\",\n        icon: \"GraduationCap\"\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/constants.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/lucide-react","vendor-chunks/zod","vendor-chunks/@swc","vendor-chunks/@hookform","vendor-chunks/tailwind-merge","vendor-chunks/react-hook-form","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fdoniyor%2FDocuments%2FSideProjects%2Fglobaling%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fdoniyor%2FDocuments%2FSideProjects%2Fglobaling&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();