import { Language, Service, Industry } from '@/types'

export const LANGUAGES: Language[] = [
  { code: 'en', name: 'English' },
  { code: 'es', name: 'Spanish' },
  { code: 'fr', name: 'French' },
  { code: 'de', name: 'German' },
  { code: 'it', name: 'Italian' },
  { code: 'pt', name: 'Portuguese' },
  { code: 'ru', name: 'Russian' },
  { code: 'zh', name: 'Chinese (Mandarin)' },
  { code: 'ja', name: 'Japanese' },
  { code: 'ko', name: 'Korean' },
  { code: 'ar', name: 'Arabic' },
  { code: 'hi', name: 'Hindi' },
  { code: 'tr', name: 'Turkish' },
  { code: 'pl', name: 'Polish' },
  { code: 'nl', name: 'Dutch' },
  { code: 'sv', name: 'Swedish' },
  { code: 'da', name: 'Danish' },
  { code: 'no', name: 'Norwegian' },
  { code: 'fi', name: 'Finnish' },
  { code: 'he', name: 'Hebrew' },
]

export const SERVICES: Service[] = [
  {
    id: 'document-translation',
    title: 'Document Translation',
    description: 'Professional translation of documents, contracts, manuals, and reports with accuracy and cultural sensitivity.',
    icon: 'FileText'
  },
  {
    id: 'website-localization',
    title: 'Website Localization',
    description: 'Complete website localization including content translation, cultural adaptation, and technical implementation.',
    icon: 'Globe'
  },
  {
    id: 'interpretation-services',
    title: 'Interpretation Services',
    description: 'Real-time interpretation for meetings, conferences, and events with certified professional interpreters.',
    icon: 'Mic'
  },
  {
    id: 'technical-translation',
    title: 'Technical Translation',
    description: 'Specialized translation for technical documentation, software, and engineering materials.',
    icon: 'Settings'
  },
  {
    id: 'legal-translation',
    title: 'Legal Translation',
    description: 'Certified legal document translation by qualified legal translators with expertise in international law.',
    icon: 'Scale'
  }
]

export const INDUSTRIES: Industry[] = [
  {
    id: 'healthcare',
    title: 'Healthcare',
    description: 'Medical documents, clinical trials, pharmaceutical documentation, and patient materials.',
    icon: 'Heart'
  },
  {
    id: 'legal',
    title: 'Legal',
    description: 'Contracts, court documents, legal correspondence, and regulatory compliance materials.',
    icon: 'Gavel'
  },
  {
    id: 'technology',
    title: 'Technology',
    description: 'Software documentation, user manuals, technical specifications, and IT content.',
    icon: 'Laptop'
  },
  {
    id: 'finance',
    title: 'Finance',
    description: 'Financial reports, banking documents, investment materials, and regulatory filings.',
    icon: 'DollarSign'
  },
  {
    id: 'education',
    title: 'Education',
    description: 'Academic papers, educational materials, course content, and institutional documents.',
    icon: 'GraduationCap'
  }
]
