interface TelegramMessage {
  fullName: string;
  companyName?: string;
  email: string;
  sourceLanguage: string;
  targetLanguage: string;
  deadline: string;
  documentUrl?: string;
  documentFileName?: string;
  additionalInfo?: string;
}

export async function sendToTelegram(data: TelegramMessage): Promise<boolean> {
  const botToken = process.env.TELEGRAM_BOT_TOKEN;
  const chatId = process.env.TELEGRAM_CHAT_ID;

  if (!botToken || !chatId) {
    console.warn('Telegram bot token or chat ID not configured');
    return false;
  }

  const message = formatTelegramMessage(data);

  try {
    const response = await fetch(`https://api.telegram.org/bot${botToken}/sendMessage`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        chat_id: chatId,
        text: message,
        parse_mode: 'HTML',
      }),
    });

    const result = await response.json();
    return result.ok;
  } catch (error) {
    console.error('Error sending message to Telegram:', error);
    return false;
  }
}

function formatTelegramMessage(data: TelegramMessage): string {
  const deadline = new Date(data.deadline).toLocaleDateString();
  
  let message = `🌐 <b>New Translation Request</b>\n\n`;
  message += `👤 <b>Name:</b> ${data.fullName}\n`;
  
  if (data.companyName) {
    message += `🏢 <b>Company:</b> ${data.companyName}\n`;
  }
  
  message += `📧 <b>Email:</b> ${data.email}\n`;
  message += `🔤 <b>From:</b> ${data.sourceLanguage}\n`;
  message += `🔤 <b>To:</b> ${data.targetLanguage}\n`;
  message += `📅 <b>Deadline:</b> ${deadline}\n`;
  
  if (data.documentUrl) {
    message += `📎 <b>Document:</b> ${data.documentFileName || 'Uploaded file'}\n`;
  }
  
  if (data.additionalInfo) {
    message += `📝 <b>Additional Info:</b>\n${data.additionalInfo}\n`;
  }
  
  message += `\n⏰ <b>Submitted:</b> ${new Date().toLocaleString()}`;
  
  return message;
}
