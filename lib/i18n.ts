import { useRouter } from 'next/router'
import { useEffect, useState } from 'react'

// Import translation files
import uzTranslations from '@/locales/uz.json'
import ruTranslations from '@/locales/ru.json'
import enTranslations from '@/locales/en.json'
import koTranslations from '@/locales/ko.json'

export type Locale = 'uz' | 'ru' | 'en' | 'ko'

export const locales: Locale[] = ['uz', 'ru', 'en', 'ko']
export const defaultLocale: Locale = 'uz'

const translations = {
  uz: uzTranslations,
  ru: ruTranslations,
  en: enTranslations,
  ko: koTranslations,
}

export function useTranslation() {
  const router = useRouter()
  const [locale, setLocale] = useState<Locale>(defaultLocale)

  useEffect(() => {
    if (router.locale && locales.includes(router.locale as Locale)) {
      setLocale(router.locale as Locale)
    }
  }, [router.locale])

  const t = (key: string): string => {
    const keys = key.split('.')
    let value: any = translations[locale]

    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k]
      } else {
        // Fallback to English if key not found
        value = translations.en
        for (const fallbackKey of keys) {
          if (value && typeof value === 'object' && fallbackKey in value) {
            value = value[fallbackKey]
          } else {
            return key // Return key if not found in fallback
          }
        }
        break
      }
    }

    return typeof value === 'string' ? value : key
  }

  const changeLanguage = (newLocale: Locale) => {
    router.push(router.asPath, router.asPath, { locale: newLocale })
  }

  return {
    t,
    locale,
    changeLanguage,
    locales,
  }
}

export function getStaticTranslations(locale: Locale) {
  return translations[locale] || translations[defaultLocale]
}

// Helper function to get direction for RTL languages
export function getDirection(locale: Locale): 'ltr' | 'rtl' {
  // Add RTL languages here if needed
  const rtlLanguages: Locale[] = []
  return rtlLanguages.includes(locale) ? 'rtl' : 'ltr'
}

// Helper function to get language name
export function getLanguageName(locale: Locale): string {
  const languageNames = {
    uz: "O'zbek",
    ru: 'Русский',
    en: 'English',
    ko: '한국어',
  }
  return languageNames[locale] || locale
}

// Helper function to format numbers based on locale
export function formatNumber(number: number, locale: Locale): string {
  const localeMap = {
    uz: 'uz-UZ',
    ru: 'ru-RU',
    en: 'en-US',
    ko: 'ko-KR',
  }
  
  return new Intl.NumberFormat(localeMap[locale]).format(number)
}

// Helper function to format dates based on locale
export function formatDate(date: Date, locale: Locale): string {
  const localeMap = {
    uz: 'uz-UZ',
    ru: 'ru-RU',
    en: 'en-US',
    ko: 'ko-KR',
  }
  
  return new Intl.DateTimeFormat(localeMap[locale], {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(date)
}
