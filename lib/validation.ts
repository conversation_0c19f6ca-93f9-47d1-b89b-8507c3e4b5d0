import { z } from 'zod'

export const translationRequestSchema = z.object({
  fullName: z.string().min(2, 'Full name must be at least 2 characters'),
  companyName: z.string().optional(),
  email: z.string().email('Please enter a valid email address'),
  sourceLanguage: z.string().min(1, 'Please select a source language'),
  targetLanguage: z.string().min(1, 'Please select a target language'),
  deadline: z.string().refine((date) => {
    const selectedDate = new Date(date);
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);
    return selectedDate >= tomorrow;
  }, 'Deadline must be at least tomorrow'),
  documentUrl: z.string().url().optional().or(z.literal('')),
  additionalInfo: z.string().optional(),
})

export type TranslationRequestInput = z.infer<typeof translationRequestSchema>
