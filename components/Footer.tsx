import { Globe, Mail, Phone, MapPin } from 'lucide-react'

export default function Footer() {
  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="col-span-1 md:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <Globe className="h-8 w-8 text-primary-400" />
              <span className="text-2xl font-bold">Globaling</span>
            </div>
            <p className="text-gray-300 mb-6 max-w-md">
              Professional translation services connecting businesses and individuals across language barriers. 
              We provide accurate, culturally-sensitive translations for all your communication needs.
            </p>
            <div className="space-y-2">
              <div className="flex items-center space-x-2 text-gray-300">
                <Mail className="h-4 w-4" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center space-x-2 text-gray-300">
                <Phone className="h-4 w-4" />
                <span>+****************</span>
              </div>
              <div className="flex items-center space-x-2 text-gray-300">
                <MapPin className="h-4 w-4" />
                <span>123 Translation Ave, Global City, GC 12345</span>
              </div>
            </div>
          </div>

          {/* Services */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Services</h3>
            <ul className="space-y-2 text-gray-300">
              <li>Document Translation</li>
              <li>Website Localization</li>
              <li>Interpretation Services</li>
              <li>Technical Translation</li>
              <li>Legal Translation</li>
            </ul>
          </div>

          {/* Industries */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Industries</h3>
            <ul className="space-y-2 text-gray-300">
              <li>Healthcare</li>
              <li>Legal</li>
              <li>Technology</li>
              <li>Finance</li>
              <li>Education</li>
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
          <p>&copy; 2024 Globaling. All rights reserved.</p>
        </div>
      </div>
    </footer>
  )
}
