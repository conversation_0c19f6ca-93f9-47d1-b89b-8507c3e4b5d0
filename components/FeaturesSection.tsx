'use client'

import { CheckCir<PERSON>, Users, Clock, Award } from 'lucide-react'
import { useTranslation } from '@/lib/i18n'

export default function FeaturesSection() {
  const { t } = useTranslation()
  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            {t('features.title')}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {t('features.subtitle')}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div className="text-center">
            <div className="bg-primary-100 rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
              <CheckCircle className="h-8 w-8 text-primary-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">{t('features.accuracy.title')}</h3>
            <p className="text-gray-600">
              {t('features.accuracy.description')}
            </p>
          </div>

          <div className="text-center">
            <div className="bg-primary-100 rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
              <Clock className="h-8 w-8 text-primary-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">{t('features.delivery.title')}</h3>
            <p className="text-gray-600">
              {t('features.delivery.description')}
            </p>
          </div>

          <div className="text-center">
            <div className="bg-primary-100 rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
              <Users className="h-8 w-8 text-primary-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">{t('features.team.title')}</h3>
            <p className="text-gray-600">
              {t('features.team.description')}
            </p>
          </div>

          <div className="text-center">
            <div className="bg-primary-100 rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
              <Award className="h-8 w-8 text-primary-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">{t('features.quality.title')}</h3>
            <p className="text-gray-600">
              {t('features.quality.description')}
            </p>
          </div>
        </div>
      </div>
    </section>
  )
}
