'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Upload, Link, CheckCircle, AlertCircle, Loader2 } from 'lucide-react'
import { translationRequestSchema, type TranslationRequestInput } from '@/lib/validation'
import { LANGUAGES } from '@/lib/constants'
import { getTomorrowDate, formatFileSize, isValidFileType } from '@/lib/utils'
import { cn } from '@/lib/utils'

export default function TranslationForm() {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle')
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [useFileUpload, setUseFileUpload] = useState(true)

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
  } = useForm<TranslationRequestInput>({
    resolver: zodResolver(translationRequestSchema),
  })

  const sourceLanguage = watch('sourceLanguage')
  const targetLanguage = watch('targetLanguage')

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      if (!isValidFileType(file)) {
        alert('Please select a valid file type (PDF, Word, Excel, PowerPoint, or Text)')
        return
      }
      if (file.size > 10 * 1024 * 1024) { // 10MB limit
        alert('File size must be less than 10MB')
        return
      }
      setSelectedFile(file)
    }
  }

  const onSubmit = async (data: TranslationRequestInput) => {
    setIsSubmitting(true)
    setSubmitStatus('idle')

    try {
      const formData = new FormData()
      
      // Add form fields
      Object.entries(data).forEach(([key, value]) => {
        if (value !== undefined && value !== '') {
          formData.append(key, value)
        }
      })

      // Add file if selected
      if (selectedFile && useFileUpload) {
        formData.append('documentFile', selectedFile)
      }

      const response = await fetch('/api/translation-request', {
        method: 'POST',
        body: formData,
      })

      const result = await response.json()

      if (result.success) {
        setSubmitStatus('success')
        reset()
        setSelectedFile(null)
      } else {
        setSubmitStatus('error')
        console.error('Submission error:', result.error)
      }
    } catch (error) {
      setSubmitStatus('error')
      console.error('Submission error:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  if (submitStatus === 'success') {
    return (
      <div className="bg-white rounded-2xl shadow-lg p-8 text-center">
        <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
        <h3 className="text-2xl font-bold text-gray-900 mb-2">Request Submitted Successfully!</h3>
        <p className="text-gray-600 mb-6">
          Thank you for your translation request. We'll review your requirements and get back to you within 24 hours.
        </p>
        <button
          onClick={() => setSubmitStatus('idle')}
          className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-lg font-medium transition-colors"
        >
          Submit Another Request
        </button>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-2xl shadow-lg p-8">
      <div className="mb-8">
        <h3 className="text-2xl font-bold text-gray-900 mb-2">Request a Translation Quote</h3>
        <p className="text-gray-600">
          Fill out the form below and we'll provide you with a detailed quote for your translation project.
        </p>
      </div>

      {submitStatus === 'error' && (
        <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4 flex items-center">
          <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
          <span className="text-red-700">
            There was an error submitting your request. Please try again.
          </span>
        </div>
      )}

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Personal Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label htmlFor="fullName" className="block text-sm font-medium text-gray-700 mb-2">
              Full Name *
            </label>
            <input
              {...register('fullName')}
              type="text"
              id="fullName"
              className={cn(
                'w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors',
                errors.fullName ? 'border-red-300' : 'border-gray-300'
              )}
              placeholder="Enter your full name"
            />
            {errors.fullName && (
              <p className="mt-1 text-sm text-red-600">{errors.fullName.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="companyName" className="block text-sm font-medium text-gray-700 mb-2">
              Company Name
            </label>
            <input
              {...register('companyName')}
              type="text"
              id="companyName"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
              placeholder="Enter your company name (optional)"
            />
          </div>
        </div>

        {/* Email */}
        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
            Email Address *
          </label>
          <input
            {...register('email')}
            type="email"
            id="email"
            className={cn(
              'w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors',
              errors.email ? 'border-red-300' : 'border-gray-300'
            )}
            placeholder="Enter your email address"
          />
          {errors.email && (
            <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
          )}
        </div>

        {/* Languages */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label htmlFor="sourceLanguage" className="block text-sm font-medium text-gray-700 mb-2">
              Source Language *
            </label>
            <select
              {...register('sourceLanguage')}
              id="sourceLanguage"
              className={cn(
                'w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors',
                errors.sourceLanguage ? 'border-red-300' : 'border-gray-300'
              )}
            >
              <option value="">Select source language</option>
              {LANGUAGES.map((lang) => (
                <option key={lang.code} value={lang.name}>
                  {lang.name}
                </option>
              ))}
            </select>
            {errors.sourceLanguage && (
              <p className="mt-1 text-sm text-red-600">{errors.sourceLanguage.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="targetLanguage" className="block text-sm font-medium text-gray-700 mb-2">
              Target Language *
            </label>
            <select
              {...register('targetLanguage')}
              id="targetLanguage"
              className={cn(
                'w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors',
                errors.targetLanguage ? 'border-red-300' : 'border-gray-300'
              )}
            >
              <option value="">Select target language</option>
              {LANGUAGES.filter(lang => lang.name !== sourceLanguage).map((lang) => (
                <option key={lang.code} value={lang.name}>
                  {lang.name}
                </option>
              ))}
            </select>
            {errors.targetLanguage && (
              <p className="mt-1 text-sm text-red-600">{errors.targetLanguage.message}</p>
            )}
          </div>
        </div>

        {/* Deadline */}
        <div>
          <label htmlFor="deadline" className="block text-sm font-medium text-gray-700 mb-2">
            Deadline *
          </label>
          <input
            {...register('deadline')}
            type="date"
            id="deadline"
            min={getTomorrowDate()}
            className={cn(
              'w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors',
              errors.deadline ? 'border-red-300' : 'border-gray-300'
            )}
          />
          {errors.deadline && (
            <p className="mt-1 text-sm text-red-600">{errors.deadline.message}</p>
          )}
        </div>

        {/* Document Upload/URL */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Document
          </label>
          <div className="mb-4">
            <div className="flex space-x-4">
              <button
                type="button"
                onClick={() => setUseFileUpload(true)}
                className={cn(
                  'px-4 py-2 rounded-lg font-medium transition-colors',
                  useFileUpload
                    ? 'bg-primary-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                )}
              >
                <Upload className="h-4 w-4 inline mr-2" />
                Upload File
              </button>
              <button
                type="button"
                onClick={() => setUseFileUpload(false)}
                className={cn(
                  'px-4 py-2 rounded-lg font-medium transition-colors',
                  !useFileUpload
                    ? 'bg-primary-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                )}
              >
                <Link className="h-4 w-4 inline mr-2" />
                Provide URL
              </button>
            </div>
          </div>

          {useFileUpload ? (
            <div>
              <input
                type="file"
                id="documentFile"
                onChange={handleFileChange}
                accept=".pdf,.doc,.docx,.txt,.xls,.xlsx,.ppt,.pptx"
                className="hidden"
              />
              <label
                htmlFor="documentFile"
                className="w-full border-2 border-dashed border-gray-300 rounded-lg p-6 text-center cursor-pointer hover:border-primary-400 transition-colors block"
              >
                {selectedFile ? (
                  <div>
                    <p className="text-sm font-medium text-gray-900">{selectedFile.name}</p>
                    <p className="text-sm text-gray-500">{formatFileSize(selectedFile.size)}</p>
                  </div>
                ) : (
                  <div>
                    <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                    <p className="text-sm text-gray-600">
                      Click to upload or drag and drop
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      PDF, Word, Excel, PowerPoint, or Text files (max 10MB)
                    </p>
                  </div>
                )}
              </label>
            </div>
          ) : (
            <input
              {...register('documentUrl')}
              type="url"
              placeholder="https://example.com/document.pdf"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
            />
          )}
        </div>

        {/* Additional Information */}
        <div>
          <label htmlFor="additionalInfo" className="block text-sm font-medium text-gray-700 mb-2">
            Additional Information
          </label>
          <textarea
            {...register('additionalInfo')}
            id="additionalInfo"
            rows={4}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
            placeholder="Please provide any additional details about your translation project..."
          />
        </div>

        {/* Submit Button */}
        <button
          type="submit"
          disabled={isSubmitting}
          className="w-full bg-primary-600 hover:bg-primary-700 disabled:bg-primary-400 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center"
        >
          {isSubmitting ? (
            <>
              <Loader2 className="h-5 w-5 animate-spin mr-2" />
              Submitting Request...
            </>
          ) : (
            'Submit Translation Request'
          )}
        </button>
      </form>
    </div>
  )
}
