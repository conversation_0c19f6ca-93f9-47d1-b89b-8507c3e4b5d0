'use client'

import { FileText, Globe, Mic, Settings, Scale } from 'lucide-react'
import { useTranslation } from '@/lib/i18n'

const iconMap = {
  FileText,
  Globe,
  Mic,
  Settings,
  Scale,
}

export default function ServicesSection() {
  const { t } = useTranslation()

  const services = [
    {
      id: 'document',
      title: t('services.document.title'),
      description: t('services.document.description'),
      icon: 'FileText'
    },
    {
      id: 'website',
      title: t('services.website.title'),
      description: t('services.website.description'),
      icon: 'Globe'
    },
    {
      id: 'interpretation',
      title: t('services.interpretation.title'),
      description: t('services.interpretation.description'),
      icon: 'Mic'
    },
    {
      id: 'technical',
      title: t('services.technical.title'),
      description: t('services.technical.description'),
      icon: 'Settings'
    },
    {
      id: 'legal',
      title: t('services.legal.title'),
      description: t('services.legal.description'),
      icon: 'Scale'
    }
  ]

  return (
    <section id="services" className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            {t('services.title')}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {t('services.subtitle')}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service) => {
            const IconComponent = iconMap[service.icon as keyof typeof iconMap]

            return (
              <div
                key={service.id}
                className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 hover:shadow-lg transition-shadow duration-300"
              >
                <div className="flex items-center mb-4">
                  <div className="bg-primary-600 rounded-lg p-3 mr-4">
                    <IconComponent className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900">
                    {service.title}
                  </h3>
                </div>
                <p className="text-gray-600 leading-relaxed">
                  {service.description}
                </p>
              </div>
            )
          })}
        </div>

        <div className="mt-12 text-center">
          <button
            onClick={() => {
              const element = document.getElementById('request-form')
              if (element) {
                element.scrollIntoView({ behavior: 'smooth' })
              }
            }}
            className="bg-primary-600 hover:bg-primary-700 text-white px-8 py-3 rounded-lg font-medium text-lg transition-colors duration-200"
          >
            {t('services.requestQuote')}
          </button>
        </div>
      </div>
    </section>
  )
}
