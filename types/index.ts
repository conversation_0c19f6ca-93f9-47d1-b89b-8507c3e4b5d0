export interface TranslationFormData {
  fullName: string;
  companyName?: string;
  email: string;
  sourceLanguage: string;
  targetLanguage: string;
  deadline: string;
  documentFile?: File;
  documentUrl?: string;
  additionalInfo?: string;
}

export interface Language {
  code: string;
  name: string;
}

export interface Service {
  id: string;
  title: string;
  description: string;
  icon: string;
}

export interface Industry {
  id: string;
  title: string;
  description: string;
  icon: string;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export type RequestStatus = 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
